<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Models\Municipality;
use Filament\FontProviders\LocalFontProvider;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class MunicipalityPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('municipality')
            ->path('municipality')
            ->tenant(Municipality::class)
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->font(
                'sst-arabic',
                url: asset(asset('fonts/font.css')),
                provider: LocalFontProvider::class,
            )
            ->viteTheme('resources/css/filament/municipalityBranch/theme.css')
            ->discoverResources(in: app_path('Filament/Municipality/Resources'), for: 'App\\Filament\\Municipality\\Resources')
            ->discoverPages(in: app_path('Filament/Municipality/Pages'), for: 'App\\Filament\\Municipality\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Municipality/Widgets'), for: 'App\\Filament\\Municipality\\Widgets')
            ->widgets([
                //
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
