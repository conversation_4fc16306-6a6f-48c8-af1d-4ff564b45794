<?php

declare(strict_types=1);

namespace App\Observers;

use App\Models\Report;
use Carbon\Carbon;

class ReportObserver
{
    /**
     * Handle the Report "created" event.
     */
    public function creating(Report $report): void
    {

        // Use the report's timestamp if it's prefilled; otherwise now()
        $createdAt = ($report->created_at instanceof Carbon)
            ? $report->created_at
            : now();

        $year = (int) $createdAt->year;
        $month = $createdAt->format('m');

        $mun = optional($report->municipality)->code ?? 'LA';

        // If you can, wrap the creation in a DB transaction so this is race-safe
        // and uncomment lockForUpdate(). Without a transaction, leave it out.
        $seq = Report::where('municipality_id', $report->municipality_id)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
                // ->lockForUpdate() // requires an outer DB::transaction(...)
            ->count() + 1;

        $report->code = sprintf('%s-%s%04d', $mun, $month, $seq);
    }

    /**
     * Handle the Report "updated" event.
     */
    public function updated(Report $report): void
    {
        //
    }

    /**
     * Handle the Report "deleted" event.
     */
    public function deleted(Report $report): void
    {
        //
    }

    /**
     * Handle the Report "restored" event.
     */
    public function restored(Report $report): void
    {
        //
    }

    /**
     * Handle the Report "force deleted" event.
     */
    public function forceDeleted(Report $report): void
    {
        //
    }
}
