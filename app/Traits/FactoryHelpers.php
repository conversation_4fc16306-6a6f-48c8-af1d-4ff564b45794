<?php

declare(strict_types=1);

namespace App\Traits;

trait FactoryHelpers
{
    protected function generatePhoneNumber($faker): string
    {
        $prefix = $faker->randomElement(['0021891', '0021892']);
        $number = $faker->numberBetween(1000000, 9999999);

        return $prefix.$number;
    }

    protected function generateLocation($faker): array
    {
        return [
            'lat' => $this->faker->latitude(19.5, 33.2),
            'lng' => $this->faker->longitude(9.3, 25.2),
        ];
    }
}
