<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Reports\Schemas;

use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ReportForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(3)
            ->components([
                Grid::make(1)
                    ->columnSpan(2)
                    ->schema([
                        Section::make()
                            ->schema([
                                Select::make('resident_id')
                                    ->label('Resident/Citizen')
                                    ->translateLabel()
                                    ->relationship('resident', 'name')
                                    ->required(),
                                TextInput::make('title')
                                    ->label('Report Title')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('content')
                                    ->label('Content')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                Select::make('type_id')
                                    ->label('Report Type')
                                    ->translateLabel()
                                    ->relationship('type', 'name')
                                    ->required(),
                                Select::make('category_id')
                                    ->label('Report Category')
                                    ->translateLabel()
                                    ->relationship('category', 'name')
                                    ->required(),
                            ]),
                        Section::make()
                            ->columns(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->multiple()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('report_media'),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(14),
                            ]),
                    ]),
            ]);
    }
}
