<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Reports\Pages;

use App\Filament\Municipality\Resources\Reports\ReportResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Override;

class ViewReport extends ViewRecord
{
    protected static string $resource = ReportResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('View Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('View Report');
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
