<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Reports\Pages;

use App\Filament\Municipality\Resources\Reports\ReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListReports extends ListRecords
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
