<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Reports\Pages;

use App\Filament\Municipality\Resources\Reports\ReportResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditReport extends EditRecord
{
    protected static string $resource = ReportResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit Report');
    }

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
