<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\MunicipalityBranches\Pages;

use App\Filament\Municipality\Resources\MunicipalityBranches\MunicipalityBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditMunicipalityBranch extends EditRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit Municipality Branch');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit Municipality Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
