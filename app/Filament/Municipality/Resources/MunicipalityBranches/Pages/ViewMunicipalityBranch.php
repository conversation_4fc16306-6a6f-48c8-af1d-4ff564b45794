<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\MunicipalityBranches\Pages;

use App\Filament\Municipality\Resources\MunicipalityBranches\MunicipalityBranchResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Override;

class ViewMunicipalityBranch extends ViewRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Municipality Branch');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Municipality Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
