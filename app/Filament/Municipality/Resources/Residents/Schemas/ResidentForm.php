<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Residents\Schemas;

use App\Models\Municipality;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ResidentForm
{
    public static function configure(Schema $schema): Schema
    {
        /** @var Municipality $municipality */
        $municipality = Filament::getTenant();

        return $schema
            ->columns(1)
            ->components([
                Section::make('')->schema([
                    TextInput::make('name')
                        ->label('Name')
                        ->translateLabel()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('phone')
                        ->label('Phone')
                        ->translateLabel()
                        ->tel()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('email')
                        ->label('Email')
                        ->translateLabel()
                        ->email()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('password')
                        ->label('Password')
                        ->translateLabel()
                        ->password()
                        ->required()
                        ->maxLength(255),
                    Select::make('type')
                        ->label('Type')
                        ->translateLabel()
                        ->required()
                        ->options(['resident' => __('Resident'), 'citizen' => __('Citizen')]),
                    Select::make('status')
                        ->label('Status')
                        ->translateLabel()
                        ->required()
                        ->options(['active' => __('Active'), 'inactive' => __('Inactive')]),
                    Select::make('municipality_branch_id')
                        ->options($municipality->branches()->where('status', 'active')->pluck('name', 'id'))
                        ->label('Municipality Branch')
                        ->translateLabel()
                        ->required()
                        ->live(),
                ])->columns(2),
            ]);
    }
}
