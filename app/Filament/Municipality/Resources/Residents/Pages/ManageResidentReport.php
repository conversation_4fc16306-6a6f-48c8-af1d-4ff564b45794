<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Residents\Pages;

use App\Filament\Municipality\Resources\Reports\ReportResource;
use App\Filament\Municipality\Resources\Reports\Tables\ReportTable;
use App\Filament\Municipality\Resources\Residents\ResidentResource;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use Override;

class ManageResidentReport extends ManageRelatedRecords
{
    protected static string $resource = ResidentResource::class;

    protected static string $relationship = 'reports';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Reports');
    }

    public function table(Table $table): Table
    {
        return ReportTable::configure($table, 'related');
    }

    public function infolist(Schema $schema): Schema
    {
        return ReportResource::infolist($schema);
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Reports');
    }

    #[Override]
    public function getBreadcrumb(): string
    {
        return __('Reports');
    }
}
