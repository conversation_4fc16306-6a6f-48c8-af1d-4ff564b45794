<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Residents\Tables;

use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ResidentTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No residents found.'))
            ->emptyStateDescription(__('You can create a new resident by clicking the button.'))
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('municipality_branch.name')
                    ->label('Municipality Branch')
                    ->translateLabel(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                //
            ]);
    }
}
