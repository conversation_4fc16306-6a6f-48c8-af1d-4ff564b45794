<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Residents;

use App\Filament\Municipality\Resources\Residents\Pages\CreateResident;
use App\Filament\Municipality\Resources\Residents\Pages\EditResident;
use App\Filament\Municipality\Resources\Residents\Pages\ListResidents;
use App\Filament\Municipality\Resources\Residents\Pages\ManageResidentReport;
use App\Filament\Municipality\Resources\Residents\Schemas\ResidentForm;
use App\Filament\Municipality\Resources\Residents\Tables\ResidentTable;
use App\Models\Resident;
use BackedEnum;
use Filament\Actions\EditAction;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use Override;

class ResidentResource extends Resource
{
    protected static ?string $model = Resident::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return ResidentForm::configure($schema);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        $table->recordActions([
            EditAction::make(),
        ]);

        return ResidentTable::configure($table);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListResidents::route('/'),
            'create' => CreateResident::route('/create'),
            'edit' => EditResident::route('/{record}/edit'),
            'reports' => ManageResidentReport::route('/{record}/reports'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditResident::class,
            ManageResidentReport::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Residents/Citizens');
    }

    #[Override]
    public static function getLabel(): string
    {
        return __('Resident/Citizen');
    }
}
