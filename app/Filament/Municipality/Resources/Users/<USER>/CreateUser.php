<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Resources\Users\Pages;

use App\Filament\Municipality\Resources\Users\UserResource;
use Filament\Resources\Pages\CreateRecord;
use Override;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Create User');
    }
}
