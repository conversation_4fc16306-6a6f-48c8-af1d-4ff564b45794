<?php

declare(strict_types=1);

namespace App\Filament\Municipality\Widgets;

use App\Models\Institution;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Models\Report;
use App\Models\Resident;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Override;

class StatsOverview extends BaseWidget
{
    #[Override]
    protected function getStats(): array
    {
        return [
            Stat::make(__('Total Municipalities'), Municipality::count())
                ->description(__('Number of municipalities'))
                ->icon('heroicon-o-building-office')
                ->color('primary'),

            Stat::make(__('Total Institutions'), Institution::count())
                ->description(__('Number of institutions'))
                ->icon('heroicon-o-academic-cap')
                ->color('warning'),

            Stat::make(__('Total Users'), User::count())
                ->description(__('Number of users'))
                ->icon('heroicon-o-users')
                ->color('success'),

            Stat::make(__('Total Residents'), Resident::count())
                ->description(__('Number of residents'))
                ->icon('heroicon-o-users')
                ->color('danger'),

            Stat::make(__('Total Municipality Branches'), MunicipalityBranch::count())
                ->description(__('Number of municipality branches'))
                ->icon('heroicon-o-users')
                ->color('gray'),

            Stat::make(__('Total Reports'), Report::count())
                ->description(__('Number of reports'))
                ->icon('heroicon-o-users')
                ->color('gray'),
        ];
    }
}
