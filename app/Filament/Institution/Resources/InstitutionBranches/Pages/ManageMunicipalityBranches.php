<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranches\Pages;

use App\Filament\Institution\Resources\InstitutionBranches\InstitutionBranchResource;
use App\Models\InstitutionBranch;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use BackedEnum;
use Exception;
use Filament\Actions\AttachAction;
use Filament\Actions\DetachAction;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Override;

class ManageMunicipalityBranches extends ManageRelatedRecords
{
    protected static string $resource = InstitutionBranchResource::class;

    protected static string $relationship = 'municipality_branches';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-users';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Municipality Branches');
    }

    #[Override]
    public function getTitle(): string
    {
        /** @var InstitutionBranch $record */
        $record = $this->getRecord();

        return $record->institution->name.' - '.$record->name;
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
            ])
            ->headerActions([
                AttachAction::make()
                    ->form([
                        Select::make('municipality_id')
                            ->label('Municipality')
                            ->translateLabel()
                            ->options(Municipality::where('status', 'active')->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                        Select::make('municipality_branches')
                            ->label('Municipality Branch')
                            ->translateLabel()
                            ->live()
                            ->options(function ($get) {
                                $municipalityId = $get('municipality_id');
                                if (! $municipalityId) {
                                    return [];
                                }

                                return MunicipalityBranch::where('municipality_id', $municipalityId)
                                    ->where('status', 'active')
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->searchable()
                            ->required(),
                    ])->action(function (array $data): void {
                        try {
                            $municipalityBranch = MunicipalityBranch::findOrFail($data['municipality_branches']);
                            if ($this->record->municipality_branches()->find($municipalityBranch->id)) {
                                Notification::make()
                                    ->title(__('Error'))
                                    ->body(__('this municipality branch is already attached.'))
                                    ->danger()
                                    ->send();

                                return;
                            }
                            $this->record->municipality_branches()->attach($municipalityBranch);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while attaching the municipality branch.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->recordActions([
                DetachAction::make(),
            ]);
    }
}
