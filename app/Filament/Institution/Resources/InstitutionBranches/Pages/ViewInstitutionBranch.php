<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranches\Pages;

use App\Filament\Institution\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Override;

class ViewInstitutionBranch extends ViewRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('View');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('View');
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
