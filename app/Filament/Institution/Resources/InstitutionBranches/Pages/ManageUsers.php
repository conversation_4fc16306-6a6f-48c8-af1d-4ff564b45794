<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranches\Pages;

use App\Filament\Institution\Resources\InstitutionBranches\InstitutionBranchResource;
use App\Models\User;
use BackedEnum;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\AttachAction;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Override;

class ManageUsers extends ManageRelatedRecords
{
    protected static string $resource = InstitutionBranchResource::class;

    protected static string $relationship = 'users';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-users';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Users');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Users');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
            ])
            ->headerActions([
                AttachAction::make()
                    ->form([
                        Select::make('users')
                            ->label('Users')
                            ->options(User::pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function (array $data): void {
                        try {
                            $user = User::findOrFail($data['users']);

                            $this->record->users()->attach($user);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while attaching the user.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->recordActions([
                Action::make('Detach')
                    ->label('Detach')
                    ->translateLabel()
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    ->requiresConfirmation()
                    ->action(function (User $record): void {
                        try {
                            $this->record->users()->detach($record);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title('Error')
                                ->body('An error occurred while detaching the user.')
                                ->danger()
                                ->send();
                        }

                    }),
            ]);
    }
}
