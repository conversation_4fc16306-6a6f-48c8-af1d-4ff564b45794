<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranches\Pages;

use App\Filament\Institution\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditInstitutionBranch extends EditRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit Institution Branch');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit Institution Branch');
    }

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
