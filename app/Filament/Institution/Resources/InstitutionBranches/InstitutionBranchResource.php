<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\InstitutionBranches;

use App\Filament\Institution\Resources\InstitutionBranches\Pages\CreateInstitutionBranch;
use App\Filament\Institution\Resources\InstitutionBranches\Pages\EditInstitutionBranch;
use App\Filament\Institution\Resources\InstitutionBranches\Pages\ListInstitutionBranches;
use App\Filament\Institution\Resources\InstitutionBranches\Pages\ManageMunicipalityBranches;
use App\Filament\Institution\Resources\InstitutionBranches\Pages\ManageUsers;
use App\Filament\Institution\Resources\InstitutionBranches\Pages\ViewInstitutionBranch;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use BackedEnum;
use Dotswan\MapPicker\Fields\Map;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class InstitutionBranchResource extends Resource
{
    protected static ?string $model = InstitutionBranch::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Branch Information'))
                            ->columns(1)
                            ->schema([
                                Select::make('institution_id')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->live()
                                    ->searchable()
                                    ->required(),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->tel()
                                    ->maxLength(255),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make(__('Status'))
                            ->columns(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListInstitutionBranches::route('/'),
            'create' => CreateInstitutionBranch::route('/create'),
            'view' => ViewInstitutionBranch::route('/{record}'),
            'edit' => EditInstitutionBranch::route('/{record}/edit'),
            'users' => ManageUsers::route('/{record}/users'),
            'manage-municipality-branches' => ManageMunicipalityBranches::route('/{record}/municipality-branches'),
        ];
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditInstitutionBranch::class,
            ManageUsers::class,
            ManageMunicipalityBranches::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Institution Branches');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    public function getTitle(): string
    {
        return __('Institution Branches');
    }
}
