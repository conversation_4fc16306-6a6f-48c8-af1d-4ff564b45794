<?php

declare(strict_types=1);

namespace App\Filament\Institution\Resources\Reports\Pages;

use App\Enums\ReportTimelineStatus;
use App\Filament\Institution\Resources\Reports\ReportResource;
use App\Models\Institution;
use App\Models\Report;
use App\Models\ReportInstitution;
use App\Models\User;
use BackedEnum;
use Exception;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Override;

class InstitutionsReport extends ManageRelatedRecords
{
    protected static string $resource = ReportResource::class;

    protected static string $relationship = 'reportInstitutions';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institutions Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Institutions Report');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('institution_branch.name')
                    ->label('Institution Branch')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'resolved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s'),
            ])
            ->recordActions([
                Action::make('detach_institution')
                    ->label('Detach Institution')
                    ->translateLabel()
                    ->icon('heroicon-o-link-slash')
                    ->requiresConfirmation()
                    ->visible(fn (ReportInstitution $record): bool => $record->institution->id === Filament::getTenant()->id)
                    ->color('danger')
                    ->action(function (ReportInstitution $record): void {
                        try {
                            $institution = $record->institution->name;
                            $report = $record->report;
                            $record->delete();
                            $this->record->timelines()->create([
                                'status' => ReportTimelineStatus::Retract->value,
                                'notes' => 'retract by '.auth()->user()->name.' from '.$institution,
                                'report_id' => $report->id,
                                'user_id' => auth()->user()->id,
                                'user_type' => User::class,
                            ]);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while detaching the institution.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->headerActions([
                Action::make('attach_institution')
                    ->label('Attach Institution')
                    ->translateLabel()
                    ->icon('heroicon-o-link')
                    ->schema([
                        Select::make('institution_branch_id')
                            ->label('Institution Branch')
                            ->translateLabel()
                            ->required()
                            ->options(function (callable $get) {
                                $institution = Institution::findOrFail(Filament::getTenant()->id);
                                if (! $institution) {
                                    return [];
                                }

                                return $institution->branches()->where('status', 'active')->pluck('name', 'id')
                                    ->toArray();
                            }),
                    ])
                    ->action(function (array $data): void {

                        /* @var Report $report */
                        $report = $this->record;
                        // dd($data['institution_id']);
                        $data['institution_id'] = Filament::getTenant()->id;
                        try {
                            // attach the institution to the report
                            if ($report->reportInstitutions()->where('institution_id', $data['institution_id'])->exists() && $data['institution_branch_id']) {
                                $report->reportInstitutions()->where('institution_id', $data['institution_id'])->update([
                                    'institution_branch_id' => $data['institution_branch_id'],
                                ]);

                                $report->timelines()->create([
                                    'status' => ReportTimelineStatus::Assigned->value,
                                    'notes' => 'attached by '.auth()->user()->name,
                                    'report_id' => $report->id,
                                    'user_id' => auth()->user()->id,
                                    'user_type' => User::class,
                                ]);

                                return;
                            }

                            // create a new report institution
                            $report->reportInstitutions()->create([
                                'institution_id' => $data['institution_id'],
                                'institution_branch_id' => $data['institution_branch_id'],
                                'status' => 'attached',
                            ]);
                            $report->timelines()->create([
                                'status' => ReportTimelineStatus::Assigned->value,
                                'notes' => 'attached by '.auth()->user()->name,
                                'report_id' => $report->id,
                                'user_id' => auth()->user()->id,
                                'user_type' => User::class,
                            ]);

                            Notification::make()
                                ->title(__('Success'))
                                ->body(__('Institution attached successfully.'))
                                ->success()
                                ->send();
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while attaching the institution.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ]);
    }
}
