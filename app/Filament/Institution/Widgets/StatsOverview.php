<?php

declare(strict_types=1);

namespace App\Filament\Institution\Widgets;

use App\Models\Report;
use Filament\Facades\Filament;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Override;

class StatsOverview extends BaseWidget
{
    #[Override]
    protected function getStats(): array
    {
        return [
            Stat::make(__('Total Reports'), 10)
                ->description(__('Number of reports assigned to institution'))
                ->icon('heroicon-o-users')
                ->color('gray')
                ->value(fn () => Report::whereHas('reportInstitutions', function ($query): void {
                    $query->where('institution_id', Filament::getTenant()->id);
                })->count()),
        ];
    }
}
