<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Users\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Hash;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('User Information'))
                    ->schema([
                        Toggle::make('is_admin')
                            ->label('Is Admin')
                            ->translateLabel()
                            ->required()
                            ->default(true)
                            ->hiddenOn('create')
                            ->columnSpan(2),
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),
                        TextInput::make('email')
                            ->label('Email')
                            ->translateLabel()
                            ->email()
                            ->required()
                            ->validationMessages([
                                'required' => __('The email field is required.'),
                                'email' => __('The email must be a valid email address.'),
                                'unique' => __('The email has already been taken.'),
                            ])
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        TextInput::make('password')
                            ->label('Password')
                            ->translateLabel()
                            ->password()
                            ->confirmed()
                            ->validationMessages([
                                'required' => __('The password field is required.'),
                                'confirmed' => __('The password confirmation does not match.'),
                            ])
                            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $operation): bool => $operation === 'create'),
                        TextInput::make('password_confirmation')
                            ->label('Password Confirmation')
                            ->translateLabel()
                            ->password()
                            ->same('password')
                            ->validationMessages([
                                'required' => __('The password confirmation field is required.'),
                                'same' => __('The password confirmation does not match.'),
                            ])
                            ->dehydrated(false)
                            ->required(fn (string $operation): bool => $operation === 'create'),
                    ])
                    ->columns(2),
            ]);
    }
}
