<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches\Schemas;

use App\Models\Institution;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class InstitutionBranchForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(3)
            ->components([
                Grid::make(1)
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Branch Information'))
                            ->columns(1)
                            ->schema([
                                Select::make('institution_id')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->live()
                                    ->searchable()
                                    ->required(),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->tel()
                                    ->maxLength(255),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make(__('Status'))
                            ->columns(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }
}
