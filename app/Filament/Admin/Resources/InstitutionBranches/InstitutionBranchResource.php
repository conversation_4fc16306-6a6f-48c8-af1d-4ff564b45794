<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches;

use App\Filament\Admin\Resources\InstitutionBranches\Pages\CreateInstitutionBranch;
use App\Filament\Admin\Resources\InstitutionBranches\Pages\EditInstitutionBranch;
use App\Filament\Admin\Resources\InstitutionBranches\Pages\ListInstitutionBranches;
use App\Filament\Admin\Resources\InstitutionBranches\Pages\ManageMunicipalityBranches;
use App\Filament\Admin\Resources\InstitutionBranches\Pages\ManageUsers;
use App\Filament\Admin\Resources\InstitutionBranches\Pages\ViewInstitutionBranch;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use BackedEnum;
use Dotswan\MapPicker\Fields\Map;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Override;

class InstitutionBranchResource extends Resource
{
    protected static ?string $model = InstitutionBranch::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-library';

    protected static ?int $navigationSort = 5;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Branch Information'))
                            ->columns(1)
                            ->schema([
                                Select::make('institution_id')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->live()
                                    ->searchable()
                                    ->required(),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->tel()
                                    ->maxLength(255),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make(__('Status'))
                            ->columns(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No institution branches found.'))
            ->emptyStateDescription(__('You can create a new institution branch by clicking the button.'))
            ->defaultGroup('institution.name')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('institution.name')
                    ->label(__('Institution'))
                    ->collapsible(),
            ])
            ->columns([
                TextColumn::make('name')
                    ->label('Institution Branch')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('location')
                    ->label('Location')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('institution_id')
                    ->label('Institution')
                    ->translateLabel()
                    ->options(Institution::all()->pluck('name', 'id'))
                    ->searchable(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListInstitutionBranches::route('/'),
            'create' => CreateInstitutionBranch::route('/create'),
            'edit' => EditInstitutionBranch::route('/{record}/edit'),
            'view' => ViewInstitutionBranch::route('/{record}'),
            'users' => ManageUsers::route('/{record}/users'),
            'manage-municipality-branches' => ManageMunicipalityBranches::route('/{record}/municipality-branches'),
        ];
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditInstitutionBranch::class,
            ManageUsers::class,
            ManageMunicipalityBranches::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Institution Branches');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Tenants');
    }

    public function getTitle(): string
    {
        return __('Institution Branches');
    }
}
