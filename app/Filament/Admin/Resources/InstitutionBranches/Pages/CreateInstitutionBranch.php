<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches\Pages;

use App\Filament\Admin\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Resources\Pages\CreateRecord;
use Override;

class CreateInstitutionBranch extends CreateRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Create Institution Branch');
    }
}
