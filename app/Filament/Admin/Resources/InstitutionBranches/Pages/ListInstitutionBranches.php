<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches\Pages;

use App\Filament\Admin\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListInstitutionBranches extends ListRecords
{
    protected static string $resource = InstitutionBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Create Institution Branch')
                ->translateLabel(),
        ];
    }
}
