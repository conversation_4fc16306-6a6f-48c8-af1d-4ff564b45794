<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches\Pages;

use App\Filament\Admin\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditInstitutionBranch extends EditRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit');
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
