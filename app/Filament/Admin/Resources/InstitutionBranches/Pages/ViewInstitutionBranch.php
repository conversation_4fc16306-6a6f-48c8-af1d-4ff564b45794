<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\InstitutionBranches\Pages;

use App\Filament\Admin\Resources\InstitutionBranches\InstitutionBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewInstitutionBranch extends ViewRecord
{
    protected static string $resource = InstitutionBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make(),
        ];
    }
}
