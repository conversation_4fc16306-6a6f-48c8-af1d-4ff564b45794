<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Institutions\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class InstitutionTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No institutions found.'))
            ->emptyStateDescription(__('You can create a new institution by clicking the button.'))
            ->columns([
                TextColumn::make('name')
                    ->label('Institution')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('branches_count')
                    ->label('Branches Count')
                    ->translateLabel()
                    ->counts('branches')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
