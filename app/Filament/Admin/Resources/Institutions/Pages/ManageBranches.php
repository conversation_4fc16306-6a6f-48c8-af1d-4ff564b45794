<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Institutions\Pages;

use App\Filament\Admin\Resources\InstitutionBranches\InstitutionBranchResource;
use App\Filament\Admin\Resources\Institutions\InstitutionResource;
use App\Models\InstitutionBranch;
use BackedEnum;
use Exception;
use Filament\Actions\CreateAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;
use Override;

class ManageBranches extends ManageRelatedRecords
{
    protected static string $resource = InstitutionResource::class;

    protected static string $relationship = 'branches';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institution Branches');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Institution Branches');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Create Institution Branch')
                    ->translateLabel()
                    ->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255),
                        TextInput::make('phone')
                            ->label('Phone')
                            ->translateLabel()
                            ->tel()
                            ->maxLength(255),
                    ])
                    ->action(function (array $data): void {
                        try {
                            $branch = InstitutionBranch::Create([
                                'name' => $data['name'],
                                'phone' => $data['phone'],
                                'institution_id' => $this->record->id,
                            ]);
                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                            Notification::make()
                                ->title(__('Error'))
                                ->body(__('An error occurred while creating the branch.'))
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->recordActions([
                EditAction::make()
                    ->label('Edit')
                    ->translateLabel()
                    ->url(fn ($record): string => InstitutionBranchResource::getUrl('edit', ['record' => $record])),
            ]);
    }
}
