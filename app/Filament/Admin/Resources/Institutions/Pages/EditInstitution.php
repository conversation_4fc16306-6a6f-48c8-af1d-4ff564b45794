<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Institutions\Pages;

use App\Filament\Admin\Resources\Institutions\InstitutionResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditInstitution extends EditRecord
{
    protected static string $resource = InstitutionResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit Institution');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit Institution');
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
