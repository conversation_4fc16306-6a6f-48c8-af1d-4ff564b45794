<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Institutions\Pages;

use App\Filament\Admin\Resources\Institutions\InstitutionResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Override;

class ViewInstitution extends ViewRecord
{
    protected static string $resource = InstitutionResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('View');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('View');
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make(),
        ];
    }
}
