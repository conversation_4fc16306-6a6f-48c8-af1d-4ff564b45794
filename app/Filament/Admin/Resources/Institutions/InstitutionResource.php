<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Institutions;

use App\Filament\Admin\Resources\Institutions\Pages\CreateInstitution;
use App\Filament\Admin\Resources\Institutions\Pages\EditInstitution;
use App\Filament\Admin\Resources\Institutions\Pages\ListInstitutions;
use App\Filament\Admin\Resources\Institutions\Pages\ManageBranches;
use App\Filament\Admin\Resources\Institutions\Pages\ManageUsers;
use App\Filament\Admin\Resources\Institutions\Pages\ViewInstitution;
use App\Models\Institution;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Override;

class InstitutionResource extends Resource
{
    protected static ?string $model = Institution::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?int $navigationSort = 4;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Institution Information'))
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->maxLength(255),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->maxLength(255),
                                TextInput::make('website')
                                    ->label('Website')
                                    ->translateLabel()
                                    ->url()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('institution_logo'),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No institutions found.'))
            ->emptyStateDescription(__('You can create a new institution by clicking the button.'))
            ->columns([
                TextColumn::make('name')
                    ->label('Institution')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('branches_count')
                    ->label('Branches Count')
                    ->translateLabel()
                    ->counts('branches')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            ViewInstitution::class,
            EditInstitution::class,
            ManageBranches::class,
            ManageUsers::class,
        ]);
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListInstitutions::route('/'),
            'create' => CreateInstitution::route('/create'),
            'edit' => EditInstitution::route('/{record}/edit'),
            'view' => ViewInstitution::route('/{record}'),
            'manage-branches' => ManageBranches::route('/{record}/branches'),
            'users' => ManageUsers::route('/{record}/users'),
        ];
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Institutions');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institutions');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Tenants');
    }

    public function getTitle(): string
    {
        return __('Institutions');
    }
}
