<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBranches;

use App\Filament\Admin\Resources\MunicipalityBranches\Pages\CreateMunicipalityBranche;
use App\Filament\Admin\Resources\MunicipalityBranches\Pages\EditMunicipalityBranche;
use App\Filament\Admin\Resources\MunicipalityBranches\Pages\ListMunicipalityBranches;
use App\Filament\Admin\Resources\MunicipalityBranches\Pages\ManageUsers;
use App\Filament\Admin\Resources\MunicipalityBranches\Pages\ViewMunicipalityBranche;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use BackedEnum;
use Dotswan\MapPicker\Fields\Map;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Override;

class MunicipalityBranchResource extends Resource
{
    protected static ?string $model = MunicipalityBranch::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?int $navigationSort = 3;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                Select::make('municipality_id')
                                    ->label('Municipality')
                                    ->translateLabel()
                                    ->options(Municipality::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ]),
                        Section::make(__('Municipality Branch Information'))
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->maxLength(255),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('municipality_branch_logo'),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(6),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No municipality branches found.'))
            ->emptyStateDescription(__('You can create a new municipality branch by clicking the button.'))
            ->defaultGroup('municipality.name')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('municipality.name')
                    ->label(__('Municipality'))
                    ->collapsible(),
            ])
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                SelectFilter::make('municipality_id')
                    ->label('Municipality')
                    ->options(Municipality::all()->pluck('name', 'id'))
                    ->searchable(),
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListMunicipalityBranches::route('/'),
            'create' => CreateMunicipalityBranche::route('/create'),
            'edit' => EditMunicipalityBranche::route('/{record}/edit'),
            'view' => ViewMunicipalityBranche::route('/{record}'),
            'users' => ManageUsers::route('/{record}/users'),
        ];
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditMunicipalityBranche::class,
            ManageUsers::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Municipality Branches');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Municipality Branches');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Tenants');
    }

    public function getTitle(): string
    {
        return __('Municipality Branches');
    }
}
