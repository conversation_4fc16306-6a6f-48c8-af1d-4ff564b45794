<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBranches\Pages;

use App\Filament\Admin\Resources\MunicipalityBranches\MunicipalityBranchResource;
use Filament\Resources\Pages\CreateRecord;
use Override;

class CreateMunicipalityBranche extends CreateRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Create Municipality Branch');
    }
}
