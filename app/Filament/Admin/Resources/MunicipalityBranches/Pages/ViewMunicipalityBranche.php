<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBranches\Pages;

use App\Filament\Admin\Resources\MunicipalityBranches\MunicipalityBranchResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewMunicipalityBranche extends ViewRecord
{
    protected static string $resource = MunicipalityBranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make(),
        ];
    }
}
