<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\MunicipalityBranches\Tables;

use App\Models\Municipality;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;

class MunicipalityBranchTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No municipality branches found.'))
            ->emptyStateDescription(__('You can create a new municipality branch by clicking the button.'))
            ->defaultGroup('municipality.name')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('municipality.name')
                    ->label(__('Municipality'))
                    ->collapsible(),
            ])
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                SelectFilter::make('municipality_id')
                    ->label('Municipality')
                    ->translateLabel()
                    ->options(Municipality::all()->pluck('name', 'id'))
                    ->searchable(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
