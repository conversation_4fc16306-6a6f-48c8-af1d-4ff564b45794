<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Reports\Tables;

use App\Enums\ReportTimelineStatus;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ReportTable
{
    public static function configure(Table $table, string $relationship = 'none'): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No reports found.'))
            ->defaultGroup('municipality.name')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('municipality.name')
                    ->label(__('Municipality'))
                    ->collapsible(),
            ])
            ->columns([
                TextColumn::make('code')
                    ->label('Code')
                    ->translateLabel()
                    ->searchable()
                    ->badge(),
                TextColumn::make('last_status')
                    ->label('Latest Status')
                    ->translateLabel()
                    ->badge()
                    ->color(fn (string $state): string => ReportTimelineStatus::from($state)->color())
                    ->getStateUsing(fn ($record) => $record->timelines()->latest()->first()?->status)
                    ->formatStateUsing(fn (string $state) => __($state)),
                TextColumn::make('title')
                    ->label('Report Title')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('resident.name')
                    ->label('Resident/Citizen')
                    ->translateLabel(),
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->translateLabel()
                    ->badge()
                    ->wrap(),
                TextColumn::make('type.name')
                    ->label('Report Type')
                    ->translateLabel(),
                TextColumn::make('category.name')
                    ->label('Report Category')
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options(ReportTimelineStatus::options())
                    ->query(function (Builder $query, array $state): Builder {
                        if ($state['value'] !== '' && $state['value'] !== null) {
                            $query->whereLatestTimelineStatus($state['value']);
                        }

                        return $query;
                    }),
            ])
            ->recordActions([
                ViewAction::make()
                    ->label('View')
                    ->translateLabel()
                    ->tooltip(__('View'))
                    ->hiddenLabel()
                    ->icon('heroicon-o-eye'),
                EditAction::make()
                    ->label('Edit')
                    ->translateLabel()
                    ->tooltip(__('Edit'))
                    ->hiddenLabel()
                    ->hidden(fn ($record): bool => $relationship === 'related')
                    ->icon('heroicon-o-pencil'),
            ])
            ->toolbarActions([
                DeleteBulkAction::make()
                    ->hidden(fn ($record): bool => $relationship === 'related'),
            ]);
    }
}
