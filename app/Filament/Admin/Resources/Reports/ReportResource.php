<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Reports;

use App\Filament\Admin\Resources\Reports\Pages\CreateReport;
use App\Filament\Admin\Resources\Reports\Pages\EditReport;
use App\Filament\Admin\Resources\Reports\Pages\InstitutionsReport;
use App\Filament\Admin\Resources\Reports\Pages\ListReports;
use App\Filament\Admin\Resources\Reports\Pages\TimelineReport;
use App\Filament\Admin\Resources\Reports\Pages\ViewReport;
use App\Filament\Admin\Resources\Reports\Schemas\ReportForm;
use App\Filament\Admin\Resources\Reports\Schemas\ReportInfolist;
use App\Filament\Admin\Resources\Reports\Tables\ReportTable;
use App\Models\Report;
use BackedEnum;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use Override;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 1;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return ReportForm::configure($schema);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return ReportTable::configure($table);
    }

    #[Override]
    public static function infolist(Schema $schema): Schema
    {
        return ReportInfolist::configure($schema);
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListReports::route('/'),
            'create' => CreateReport::route('/create'),
            'view' => ViewReport::route('/{record}'),
            'edit' => EditReport::route('/{record}/edit'),
            'timeline' => TimelineReport::route('/{record}/timeline'),
            'institutions' => InstitutionsReport::route('/{record}/institutions'),
        ];
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            ViewReport::class,
            EditReport::class,
            TimelineReport::class,
            InstitutionsReport::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Reports');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Reports');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Reports');
    }

    public function getTitle(): string
    {
        return __('Reports');
    }
}
