<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Reports\Pages;

use App\Filament\Admin\Resources\Reports\ReportResource;
use BackedEnum;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class TimelineReport extends ManageRelatedRecords
{
    protected static string $resource = ReportResource::class;

    protected static string $relationship = 'timelines';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Timeline Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Timeline Report');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with('user')->orderBy('created_at', 'desc'))
            ->columns([
                TextColumn::make('notes')
                    ->label('Notes')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'assigned' => 'gray',
                        'resolved' => 'success',
                        'rejected' => 'danger',
                        'retract' => 'warning',
                        default => 'blue',
                    }),
                TextColumn::make('user')
                    ->label('User')
                    ->translateLabel()
                    ->formatStateUsing(fn ($record): string => __(class_basename($record->user_type)).' / '.optional($record->user)->name)
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s'),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s'),
            ]);
    }
}
