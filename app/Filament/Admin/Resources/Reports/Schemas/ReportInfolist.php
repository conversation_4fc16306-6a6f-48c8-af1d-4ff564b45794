<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Reports\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ReportInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('Report Details'))
                    ->columns(2)
                    ->schema([
                        TextEntry::make('code')
                            ->label('Code')
                            ->translateLabel()
                            ->badge(),
                        TextEntry::make('title')
                            ->label('Report Title')
                            ->translateLabel(),

                        TextEntry::make('resident.name')
                            ->label('Resident/Citizen')
                            ->translateLabel(),

                        TextEntry::make('type.name')
                            ->label('Report Type')
                            ->translateLabel(),

                        TextEntry::make('category.name')
                            ->label('Report Category')
                            ->translateLabel(),

                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->translateLabel()
                            ->dateTime(),

                        TextEntry::make('nearest_address')
                            ->label('Nearest Address')
                            ->translateLabel(),
                    ]),

                Section::make(__('Content'))
                    ->schema([
                        TextEntry::make('content')
                            ->label('Report Content')
                            ->translateLabel()
                            ->columnSpanFull(),
                    ]),

                Section::make(__('Timeline Report'))
                    ->schema([
                        ViewEntry::make('timeline')
                            ->label('Activity')
                            ->view('filament.custom.report-timeline'),
                    ]),

                Section::make(__('Media'))
                    ->schema([
                        ViewEntry::make('report_media')
                            ->label('Attached Media')
                            ->view('filament.custom.report-media-gallery'),
                    ]),

                Section::make(__('Location'))
                    ->schema([
                        ViewEntry::make('location')
                            ->label('Map')
                            ->view('filament.custom.location-map'),
                    ]),
            ]);
    }
}
