<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\FAQS\Pages;

use App\Filament\Admin\Resources\FAQS\FAQResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListFAQS extends ListRecords
{
    protected static string $resource = FAQResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
