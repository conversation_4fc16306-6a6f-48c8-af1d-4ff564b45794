<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\FAQS;

use App\Filament\Admin\Resources\FAQS\Pages\CreateFAQ;
use App\Filament\Admin\Resources\FAQS\Pages\EditFAQ;
use App\Filament\Admin\Resources\FAQS\Pages\ListFAQS;
use App\Filament\Admin\Resources\FAQS\Schemas\FAQSForm;
use App\Filament\Admin\Resources\FAQS\Tables\FAQSTable;
use App\Models\FAQ;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use Override;

class FAQResource extends Resource
{
    protected static ?string $model = FAQ::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return FAQSForm::configure($schema);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return FAQSTable::configure($table);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListFAQS::route('/'),
            'create' => CreateFAQ::route('/create'),
            'edit' => EditFAQ::route('/{record}/edit'),
        ];
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('FAQs');
    }

    #[Override]
    public static function getLabel(): string
    {
        return __('FAQ');
    }
}
