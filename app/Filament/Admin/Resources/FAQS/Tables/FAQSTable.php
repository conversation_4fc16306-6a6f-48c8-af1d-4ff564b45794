<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\FAQS\Tables;

use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class FAQSTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('question')
                    ->translateLabel()
                    ->label('Question')
                    ->wrap(),
                TextColumn::make('answer')
                    ->translateLabel()
                    ->label('Answer')
                    ->wrap(),
                IconColumn::make('is_active')
                    ->label('Is active')
                    ->translateLabel()
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([

            ]);
    }
}
