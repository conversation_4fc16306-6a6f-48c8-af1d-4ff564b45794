<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\FAQS\Schemas;

use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class FAQSForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('FAQ Information'))
                    ->columns(1)
                    ->schema([
                        Toggle::make('is_active')
                            ->translateLabel()
                            ->label('Is active')
                            ->default(true)
                            ->required(),
                        Textarea::make('question')
                            ->translateLabel()
                            ->label('Question')
                            ->required()
                            ->columnSpanFull(),
                        Textarea::make('answer')
                            ->translateLabel()
                            ->label('Answer')
                            ->required()
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
