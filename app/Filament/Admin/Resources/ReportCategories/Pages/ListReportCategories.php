<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportCategories\Pages;

use App\Filament\Admin\Resources\ReportCategories\ReportCategoryResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListReportCategories extends ListRecords
{
    protected static string $resource = ReportCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Create Report Category')
                ->translateLabel(),
        ];
    }
}
