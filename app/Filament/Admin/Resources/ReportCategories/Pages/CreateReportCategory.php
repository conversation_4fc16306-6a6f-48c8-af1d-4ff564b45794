<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportCategories\Pages;

use App\Filament\Admin\Resources\ReportCategories\ReportCategoryResource;
use Filament\Resources\Pages\CreateRecord;
use Override;

class CreateReportCategory extends CreateRecord
{
    protected static string $resource = ReportCategoryResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Create Report Category');
    }
}
