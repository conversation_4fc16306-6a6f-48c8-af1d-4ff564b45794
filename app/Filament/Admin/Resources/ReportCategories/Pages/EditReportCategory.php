<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportCategories\Pages;

use App\Filament\Admin\Resources\ReportCategories\ReportCategoryResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditReportCategory extends EditRecord
{
    protected static string $resource = ReportCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
