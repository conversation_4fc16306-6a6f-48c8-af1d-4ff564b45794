<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportCategories;

use App\Filament\Admin\Resources\ReportCategories\Pages\CreateReportCategory;
use App\Filament\Admin\Resources\ReportCategories\Pages\EditReportCategory;
use App\Filament\Admin\Resources\ReportCategories\Pages\ListReportCategories;
use App\Models\ReportCategory;
use BackedEnum;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class ReportCategoryResource extends Resource
{
    protected static ?string $model = ReportCategory::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 2;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Section::make()
                            ->columnSpan(2)
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ]);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Report Category')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                //
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListReportCategories::route('/'),
            'create' => CreateReportCategory::route('/create'),
            'edit' => EditReportCategory::route('/{record}/edit'),
        ];
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Report Categories');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Report Categories');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Reports');
    }

    public function getTitle(): string
    {
        return __('Report Categories');
    }
}
