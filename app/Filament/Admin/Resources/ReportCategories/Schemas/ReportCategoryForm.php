<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportCategories\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ReportCategoryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Grid::make(1)
                    ->columns(3)
                    ->schema([
                        Section::make()
                            ->columnSpan(2)
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                        Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ]);
    }
}
