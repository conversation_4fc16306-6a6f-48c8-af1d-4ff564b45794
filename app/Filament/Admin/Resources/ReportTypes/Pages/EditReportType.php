<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportTypes\Pages;

use App\Filament\Admin\Resources\ReportTypes\ReportTypeResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditReportType extends EditRecord
{
    protected static string $resource = ReportTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
