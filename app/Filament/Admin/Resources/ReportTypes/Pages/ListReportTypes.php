<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportTypes\Pages;

use App\Filament\Admin\Resources\ReportTypes\ReportTypeResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListReportTypes extends ListRecords
{
    protected static string $resource = ReportTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Create Report Type')
                ->translateLabel(),
        ];
    }
}
