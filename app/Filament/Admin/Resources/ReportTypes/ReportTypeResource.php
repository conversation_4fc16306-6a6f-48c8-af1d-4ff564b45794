<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportTypes;

use App\Filament\Admin\Resources\ReportTypes\Pages\CreateReportType;
use App\Filament\Admin\Resources\ReportTypes\Pages\EditReportType;
use App\Filament\Admin\Resources\ReportTypes\Pages\ListReportTypes;
use App\Models\Institution;
use App\Models\ReportCategory;
use App\Models\ReportType;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class ReportTypeResource extends Resource
{
    protected static ?string $model = ReportType::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 3;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->schema([
                        Section::make()
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('icon')
                                    ->label('Icon')
                                    ->translateLabel()
                                    ->maxLength(255),
                                Select::make('category_id')
                                    ->label('Report Category')
                                    ->translateLabel()
                                    ->options(ReportCategory::all()->pluck('name', 'id'))
                                    ->required(),
                                Select::make('institution')
                                    ->label('Institution')
                                    ->translateLabel()
                                    ->relationship('institution', 'name')
                                    ->options(Institution::all()->pluck('name', 'id'))
                                    ->multiple(),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->required()
                                    ->options(['active' => __('Active'), 'inactive' => __('Inactive')]),
                            ]),
                    ]),
            ]);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Report Type')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('category.name')
                    ->label('Report Category')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->badge()
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->formatStateUsing(fn ($state): string => __($state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListReportTypes::route('/'),
            'create' => CreateReportType::route('/create'),
            'edit' => EditReportType::route('/{record}/edit'),
        ];
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Report Types');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Report Types');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Reports');
    }

    public function getTitle(): string
    {
        return __('Report Types');
    }
}
