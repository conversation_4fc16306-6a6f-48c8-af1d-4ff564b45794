<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\ReportTypes\Schemas;

use App\Models\ReportCategory;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ReportTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Grid::make(1)
                    ->schema([
                        Section::make()
                            ->columns(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('icon')
                                    ->label('Icon')
                                    ->translateLabel()
                                    ->maxLength(255),
                                Select::make('category_id')
                                    ->label('Report Category')
                                    ->translateLabel()
                                    ->searchable()
                                    ->options(ReportCategory::where('status', 'active')->pluck('name', 'id'))
                                    ->required(),
                                Select::make('institutions')
                                    ->label(__('Institution'))
                                    ->translateLabel()
                                    ->multiple()
                                    ->relationship(
                                        name: 'institutions',
                                        titleAttribute: 'name',
                                        modifyQueryUsing: fn ($query) => $query->select('id', 'name')->where('status', 'active')
                                    )
                                    ->preload()
                                    ->searchable(),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->required()
                                    ->options(['active' => __('Active'), 'inactive' => __('Inactive')]),
                            ]),
                    ]),
            ]);
    }
}
