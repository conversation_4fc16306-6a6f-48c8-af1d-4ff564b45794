<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Municipalities\Pages;

use App\Filament\Admin\Resources\Municipalities\MunicipalityResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Override;

class ViewMunicipality extends ViewRecord
{
    protected static string $resource = MunicipalityResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('View');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('View');
    }

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
            DeleteAction::make(),
        ];
    }
}
