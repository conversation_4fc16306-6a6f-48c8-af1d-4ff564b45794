<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Municipalities\Pages;

use App\Filament\Admin\Resources\Municipalities\MunicipalityResource;
use Filament\Resources\Pages\CreateRecord;
use Override;

class CreateMunicipality extends CreateRecord
{
    protected static string $resource = MunicipalityResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Create Municipality');
    }
}
