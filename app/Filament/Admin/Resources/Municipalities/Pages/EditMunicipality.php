<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Municipalities\Pages;

use App\Filament\Admin\Resources\Municipalities\MunicipalityResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditMunicipality extends EditRecord
{
    protected static string $resource = MunicipalityResource::class;

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Edit Municipality');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Edit Municipality');
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
