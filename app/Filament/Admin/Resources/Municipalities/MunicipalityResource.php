<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Municipalities;

use App\Filament\Admin\Resources\Municipalities\Pages\CreateMunicipality;
use App\Filament\Admin\Resources\Municipalities\Pages\EditMunicipality;
use App\Filament\Admin\Resources\Municipalities\Pages\ListMunicipalities;
use App\Filament\Admin\Resources\Municipalities\Pages\ManageBranches;
use App\Filament\Admin\Resources\Municipalities\Pages\ManageUsers;
use App\Filament\Admin\Resources\Municipalities\Pages\ViewMunicipality;
use App\Models\Municipality;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Override;

class MunicipalityResource extends Resource
{
    protected static ?string $model = Municipality::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office';

    protected static ?int $navigationSort = 2;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make(__('Municipality Information'))
                            ->columns(1)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Name')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->maxLength(255),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->maxLength(255),
                                TextInput::make('website')
                                    ->label('Website')
                                    ->translateLabel()
                                    ->url()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                TextInput::make('code')
                                    ->label('Code')
                                    ->translateLabel()
                                    ->required()
                                    ->maxValue(2)
                                    ->minLength(1),
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('municipality_logo'),
                                Select::make('status')
                                    ->label('Status')
                                    ->translateLabel()
                                    ->options([
                                        'active' => __('Active'),
                                        'inactive' => __('Inactive'),
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),
            ])->columns(3);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('branches_count')
                    ->label('Branches Count')
                    ->translateLabel()
                    ->counts('branches')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            ViewMunicipality::class,
            EditMunicipality::class,
            ManageBranches::class,
            ManageUsers::class,
        ]);
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListMunicipalities::route('/'),
            'create' => CreateMunicipality::route('/create'),
            'edit' => EditMunicipality::route('/{record}/edit'),
            'view' => ViewMunicipality::route('/{record}'),
            'manage-branches' => ManageBranches::route('/{record}/branches'),
            'users' => ManageUsers::route('/{record}/users'),
        ];
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Municipalities');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Municipalities');
    }

    #[Override]
    public static function getNavigationGroup(): ?string
    {
        return __('Tenants');
    }

    public function getTitle(): string
    {
        return __('Municipalities');
    }
}
