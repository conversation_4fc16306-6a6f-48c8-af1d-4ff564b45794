<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Residents\Tables;

use App\Models\Resident;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;

class ResidentTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->emptyStateHeading(__('No residents found.'))
            ->emptyStateDescription(__('You can create a new resident by clicking the button.'))
            ->defaultGroup('municipality.name')
            ->groupingSettingsHidden()
            ->groups([
                Group::make('municipality.name')
                    ->label(__('Municipality'))
                    ->collapsible(),
            ])
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('municipality_branch.name')
                    ->label('Municipality Branch')
                    ->translateLabel(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => __('Active'),
                        'inactive' => __('Inactive'),
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                Action::make('verify')
                    ->label('Verify')
                    ->translateLabel()
                    ->icon('heroicon-o-check-circle')
                    ->requiresConfirmation()
                    ->iconButton()
                    ->tooltip(fn (Resident $record): string => $record->verified_at === null ? __('Verify now') : __('Verified'))
                    ->disabled(fn (Resident $record): bool => $record->verified_at !== null)
                    ->color(fn (Resident $record): string => $record->verified_at === null ? 'gray' : 'success')
                    ->action(function (Resident $record): void {
                        $record->update([
                            'verified_at' => now(),
                        ]);
                    }),
            ])
            ->toolbarActions([
                //
            ]);
    }
}
