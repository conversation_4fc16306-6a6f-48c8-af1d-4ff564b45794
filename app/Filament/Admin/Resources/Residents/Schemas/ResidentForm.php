<?php

declare(strict_types=1);

namespace App\Filament\Admin\Resources\Residents\Schemas;

use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Models\Resident;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Hash;

class ResidentForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('Resident Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Name')
                            ->translateLabel()
                            ->validationMessages([
                                'required' => __('The name field is required.'),
                            ])
                            ->required()
                            ->maxLength(255),
                        TextInput::make('phone')
                            ->label('Phone')
                            ->translateLabel()
                            ->unique(Resident::class, 'phone')
                            ->tel()
                            ->validationMessages([
                                'unique' => __('The phone has already been taken.'),
                                'phone' => __('The phone must be a valid phone number.'),
                                'required' => __('The phone field is required.'),
                                'regex' => __('The phone must be a valid phone number.'),
                            ])
                            ->required()
                            ->maxLength(255),
                        TextInput::make('email')
                            ->label('Email')
                            ->translateLabel()
                            ->email()
                            ->maxLength(255),
                        Select::make('status')
                            ->label('Status')
                            ->translateLabel()
                            ->required()
                            ->options(['active' => __('Active'), 'inactive' => __('Inactive')])
                            ->default('active'),
                    ])->columns(2),
                Section::make(__('Authentication information'))
                    ->schema([
                        TextInput::make('password')
                            ->label(__('Password'))
                            ->translateLabel()
                            ->password()
                            ->confirmed()
                            ->validationMessages([
                                'required' => __('The password field is required.'),
                                'confirmed' => __('The password confirmation does not match.'),
                            ])
                            ->dehydrateStateUsing(fn ($state) => filled($state) ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $operation): bool => $operation === 'create'),

                        TextInput::make('password_confirmation')
                            ->label(__('Confirm Password'))
                            ->translateLabel()
                            ->password()
                            ->validationMessages([
                                'required' => __('The password confirmation field is required.'),
                                'same' => __('The password confirmation does not match.'),
                            ])
                            ->same('password')
                            ->required(fn (callable $get): bool => filled($get('password')))
                            ->dehydrated(false),
                    ]),
                Section::make(__('Municipality Information'))
                    ->columns(2)
                    ->schema([
                        Select::make('municipality_id')
                            ->options(Municipality::where('status', '=', 'active')->pluck('name', 'id'))
                            ->label('Municipality')
                            ->dehydrated(false)
                            ->translateLabel()
                            ->formatStateUsing(function ($record) {
                                if ($record && $record->municipality) {
                                    return $record->municipality->id;
                                }

                                return null;
                            })
                            ->live(true)
                            ->required(),
                        Select::make('municipality_branch_id')
                            ->relationship('municipality_branch', 'name')
                            ->visible(fn (callable $get) => filled($get('municipality_id')))
                            ->options(function (callable $get) {
                                $municipalityId = $get('municipality_id');

                                if (! $municipalityId) {
                                    return [];
                                }

                                return MunicipalityBranch::where('status', 'active')
                                    ->where('municipality_id', $municipalityId)
                                    ->pluck('name', 'id');
                            })
                            ->label('Municipality Branch')
                            ->translateLabel()
                            ->validationMessages([
                                'required' => __('The municipality branch field is required.'),
                            ])
                            ->required()
                            ->live(),
                    ]),
            ]);
    }
}
