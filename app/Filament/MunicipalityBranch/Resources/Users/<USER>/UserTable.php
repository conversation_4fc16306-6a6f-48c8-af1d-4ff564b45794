<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\Users\Tables;

use App\Models\User;
use Filament\Actions\DetachAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class UserTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                DetachAction::make()
                    ->visible(fn (User $record): bool => auth()->user()->id !== $record->id)
                    ->action(function (User $record): void {
                        $record->municipalityBranch()->detach();
                    }),
            ]);
    }
}
