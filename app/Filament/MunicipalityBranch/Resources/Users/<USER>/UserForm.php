<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\Users\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Hash;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Name')
                    ->translateLabel()
                    ->required()
                    ->maxLength(255),
                TextInput::make('email')
                    ->label('Email')
                    ->translateLabel()
                    ->email()
                    ->required()
                    ->maxLength(255),
                TextInput::make('password')
                    ->label('Password')
                    ->translateLabel()
                    ->password()
                    ->required()
                    ->same('password_confirmation')
                    ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                    ->dehydrated(fn ($state) => filled($state))
                    ->maxLength(255),
                TextInput::make('password_confirmation')
                    ->label('Password Confirmation')
                    ->translateLabel()
                    ->password()
                    ->required()
                    ->dehydrated(false)
                    ->maxLength(255),
            ]);
    }
}
