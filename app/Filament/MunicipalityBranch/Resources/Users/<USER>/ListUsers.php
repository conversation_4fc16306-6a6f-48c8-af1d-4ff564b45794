<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\Users\Pages;

use App\Filament\MunicipalityBranch\Resources\Users\UserResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Create User')
                ->translateLabel(),
        ];
    }
}
