<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\Reports\Pages;

use App\Enums\ReportTimelineStatus;
use App\Filament\MunicipalityBranch\Resources\Reports\ReportResource;
use App\Filament\Sheared\Schemas\TimeLineForm;
use App\Models\Report;
use App\Models\User;
use App\Services\FCMService;
use BackedEnum;
use Exception;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\MessagingException;
use Override;

class TimelineReport extends ManageRelatedRecords
{
    protected static string $resource = ReportResource::class;

    protected static string $relationship = 'timelines';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-list-bullet';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Timeline Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Timeline Report');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with('user')->latest('created_at'))
            ->columns($this->getColumns())
            ->headerActions([$this->getAddTimelineAction()]);
    }

    protected function getColumns(): array
    {
        return [
            TextColumn::make('notes')
                ->label(__('Notes'))
                ->searchable(),

            TextColumn::make('status')
                ->label(__('Status'))
                ->badge()
                ->formatStateUsing(fn (string $state) => __($state))
                ->color(fn (string $state): string => match ($state) {
                    'assigned' => 'gray',
                    'resolved' => 'success',
                    'rejected' => 'danger',
                    'retract' => 'warning',
                    default => 'blue',
                }),

            TextColumn::make('user')
                ->label(__('User'))
                ->formatStateUsing(fn ($record): string => __(class_basename($record->user_type)).' / '.optional($record->user)->name)
                ->searchable(),

            TextColumn::make('created_at')
                ->label(__('Created At'))
                ->dateTime('d M Y H:i:s'),

            TextColumn::make('updated_at')
                ->label(__('Updated At'))
                ->dateTime('d M Y H:i:s'),
        ];
    }

    protected function getAddTimelineAction(): Action
    {
        return Action::make('add_timeline')
            ->label(__('Add New Status'))
            ->icon('heroicon-o-plus')
            ->hidden(fn (): bool => $this->record->lastTimeline()->status === ReportTimelineStatus::Resolved->value)
            ->schema($this->getTimelineForm())
            ->action(fn (array $data) => $this->handleTimelineSubmission($data));
    }

    protected function getTimelineForm(): array
    {
        return TimeLineForm::getTimelineForm();
    }

    protected function handleTimelineSubmission(array $data): void
    {
        $user = auth()->user();

        /* @var Report $report */
        $report = $this->record;

        if ($data['status'] === ReportTimelineStatus::Assigned->value) {
            $alreadyAttached = $report->reportInstitutions()
                ->where('institution_id', $data['institution_id'])
                ->when($data['institution_branch_id'], fn ($query) => $query->where('institution_branch_id', $data['institution_branch_id'])
                )
                ->exists();

            if ($alreadyAttached) {
                $this->sendNotification('Error', __('This institution is already attached.'), 'danger');

                return;
            }

            $report->reportInstitutions()->create([
                'institution_id' => $data['institution_id'],
                'institution_branch_id' => $data['institution_branch_id'],
                'status' => 'attached',
            ]);
        }

        $report->timelines()->create([
            'status' => $data['status'],
            'notes' => $data['notes'],
            'report_id' => $report->id,
            'user_id' => $user->id,
            'user_type' => User::class,
        ]);

        $this->sendNotification('Success', __('Status added successfully.'));
        $this->sendFCMNotification(__('Status Updated'),
            __('The status of your report #:code has been updated to :status.', ['code' => $report->code, 'status' => __($data['status'])]),
            $report);
    }

    protected function sendNotification(string $title, string $body, string $type = 'success'): void
    {
        Notification::make()
            ->title(__($title))
            ->body($body)
            ->{$type}()
            ->send();
    }

    /**
     * Send FCM notification to all device tokens
     */
    protected function sendFCMNotification(string $title, string $body, $report): void
    {
        // get all device tokens
        $tokens = $report->resident->deviceTokens()->pluck('token')->toArray();

        if (empty($tokens)) {
            return;
        }

        // send notification
        try {
            app(FCMService::class)->sendFCMWithData($tokens, $title, $body, [
                'report_id' => $report->id,
            ]);
        } catch (MessagingException|FirebaseException|Exception) {
            Notification::make()
                ->title(__('Error'))
                ->body(__('An Error FCM occurred while sending the notification.'))
                ->danger()
                ->send();
        }
    }
}
