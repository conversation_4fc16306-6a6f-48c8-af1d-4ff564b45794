<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Resources\Reports\Pages;

use App\Filament\MunicipalityBranch\Resources\Reports\ReportResource;
use BackedEnum;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class InstitutionsReport extends ManageRelatedRecords
{
    protected static string $resource = ReportResource::class;

    protected static string $relationship = 'reportInstitutions';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-building-office';

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Institutions Report');
    }

    #[Override]
    public function getTitle(): string
    {
        return __('Institutions Report');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('institution_branch.name')
                    ->label('Institution Branch')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state) => __($state))
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->translateLabel()
                    ->dateTime('d M Y H:i:s'),
            ]);
    }
}
