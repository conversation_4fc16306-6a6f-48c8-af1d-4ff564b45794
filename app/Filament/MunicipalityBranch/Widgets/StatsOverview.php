<?php

declare(strict_types=1);

namespace App\Filament\MunicipalityBranch\Widgets;

use App\Models\MunicipalityBranch;
use App\Models\Report;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Override;

class StatsOverview extends BaseWidget
{
    #[Override]
    protected function getStats(): array
    {
        return [
            Stat::make(__('Total Municipality Branches'), MunicipalityBranch::count())
                ->description(__('Number of municipality branches'))
                ->icon('heroicon-o-users')
                ->color('gray'),

            Stat::make(__('Total Reports'), Report::count())
                ->description(__('Number of reports'))
                ->icon('heroicon-o-users')
                ->color('gray'),
        ];
    }
}
