<?php

declare(strict_types=1);

namespace App\Filament\Sheared\Schemas;

use App\Enums\ReportTimelineStatus;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use App\Models\MunicipalityBranch;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;

class TimeLineForm
{
    public static function getTimelineForm(): array
    {
        return [
            Select::make('status')
                ->label(__('Status'))
                ->live()
                ->options(function () {
                    $options = [
                        ReportTimelineStatus::Resolved->value => __('Resolved'),
                        ReportTimelineStatus::Rejected->value => __('Rejected'),
                        ReportTimelineStatus::Retract->value => __('Retract'),
                    ];

                    // this only for branch tenant
                    if (self::checkTenantType()) {
                        return $options;
                    }

                    return array_merge($options, [
                        ReportTimelineStatus::Assigned->value => __('Assigned'),
                    ]);
                })
                ->required(),

            Select::make('institution_id')
                ->label(__('Institution'))
                ->searchable()
                ->preload()
                ->options(function () {
                    $tenant = Filament::getTenant();

                    if ($tenant instanceof Institution) {
                        return [
                            $tenant->id => $tenant->name,
                        ];
                    }

                    if ($tenant instanceof InstitutionBranch) {
                        return [
                            $tenant->institution->id => $tenant->institution->name,
                        ];
                    }

                    return Institution::pluck('name', 'id');
                })
                ->visible(fn (callable $get): bool => $get('status') === ReportTimelineStatus::Assigned->value)
                ->live()
                ->required(),

            Select::make('institution_branch_id')
                ->label(__('Institution Branch'))
                ->options(function (callable $get) {
                    $institutionId = $get('institution_id');

                    if (! $institutionId) {
                        return [];
                    }

                    return InstitutionBranch::query()
                        ->where('institution_id', $institutionId)
                        ->where('status', 'active')
                        ->pluck('name', 'id');
                })
                ->visible(function (callable $get): bool {
                    if ($get('status') !== ReportTimelineStatus::Assigned->value) {
                        return false;
                    }

                    return filled($get('institution_id'));
                })
                ->required(),

            Textarea::make('notes')
                ->label(__('Notes'))
                ->required()
                ->maxLength(255),
        ];
    }

    public static function checkTenantType(): bool
    {
        $tenant = Filament::getTenant();

        return $tenant instanceof InstitutionBranch || $tenant instanceof MunicipalityBranch;
    }
}
