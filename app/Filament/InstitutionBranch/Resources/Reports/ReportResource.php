<?php

declare(strict_types=1);

namespace App\Filament\InstitutionBranch\Resources\Reports;

use App\Filament\InstitutionBranch\Resources\ReportResource\Pages;
use App\Filament\InstitutionBranch\Resources\Reports\Pages\ListReports;
use App\Filament\InstitutionBranch\Resources\Reports\Pages\TimelineReport;
use App\Filament\InstitutionBranch\Resources\Reports\Pages\ViewReport;
use App\Models\Report;
use BackedEnum;
use Dotswan\MapPicker\Fields\Map;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Override;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = -1;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    #[Override]
    public static function form(Schema $schema): Schema
    {
        return $schema
            ->columns(3)
            ->components([
                Grid::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make()
                            ->schema([
                                Select::make('resident_id')
                                    ->label('Resident/Citizen')
                                    ->translateLabel()
                                    ->relationship('resident', 'name')
                                    ->required(),
                                TextInput::make('title')
                                    ->label('Report Title')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('content')
                                    ->label('Content')
                                    ->translateLabel()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                    ]),
                Grid::make()
                    ->columnSpan(1)
                    ->schema([
                        Section::make()
                            ->columns(1)
                            ->schema([
                                Select::make('type_id')
                                    ->label('Report Type')
                                    ->translateLabel()
                                    ->relationship('type', 'name')
                                    ->required(),
                                Select::make('category_id')
                                    ->label('Report Category')
                                    ->translateLabel()
                                    ->relationship('category', 'name')
                                    ->required(),
                            ]),
                        Section::make()
                            ->columns(1)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('Logo')
                                    ->label('Logo')
                                    ->translateLabel()
                                    ->disk('s3')
                                    ->previewable()
                                    ->downloadable()
                                    ->multiple()
                                    ->acceptedFileTypes(['image/*'])
                                    ->visibility('private')
                                    ->collection('report_media'),
                                Map::make('location')
                                    ->label('Location')
                                    ->translateLabel()
                                    ->defaultLocation(latitude: 32.3152506, longitude: 15.0148311)
                                    ->showMarker(true)
                                    ->clickable(true)
                                    ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                                    ->zoom(14),
                            ]),
                    ]),
            ]);
    }

    #[Override]
    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('code')
                    ->label('Code')
                    ->translateLabel()
                    ->searchable()
                    ->badge(),
                TextColumn::make('title')
                    ->label('Report Title')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('resident.name')
                    ->label('Resident/Citizen')
                    ->translateLabel(),
                TextColumn::make('institution.name')
                    ->label('Institution')
                    ->translateLabel()
                    ->badge(),
                TextColumn::make('nearest_address')
                    ->label('Nearest Address')
                    ->translateLabel()
                    ->searchable(),
                TextColumn::make('type.name')
                    ->label('Report Type')
                    ->translateLabel(),
                TextColumn::make('category.name')
                    ->label('Report Category')
                    ->translateLabel(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
            ])
            ->toolbarActions([
                //
            ]);
    }

    #[Override]
    public static function infolist(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Section::make(__('Report Details'))
                    ->columns(2)
                    ->schema([
                        TextEntry::make('code')
                            ->label('Code')
                            ->translateLabel()
                            ->badge(),
                        TextEntry::make('title')
                            ->label('Report Title')
                            ->translateLabel(),

                        TextEntry::make('resident.name')
                            ->label('Resident/Citizen')
                            ->translateLabel(),

                        TextEntry::make('type.name')
                            ->label('Report Type')
                            ->translateLabel(),

                        TextEntry::make('category.name')
                            ->label('Report Category')
                            ->translateLabel(),

                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->translateLabel()
                            ->dateTime(),

                        TextEntry::make('nearest_address')
                            ->label('Nearest Address')
                            ->translateLabel(),
                    ]),

                Section::make(__('Content'))
                    ->schema([
                        TextEntry::make('content')
                            ->label('Report Content')
                            ->translateLabel()
                            ->columnSpanFull(),
                    ]),

                Section::make(__('Timeline Report'))
                    ->schema([
                        ViewEntry::make('timeline')
                            ->label('Activity')
                            ->view('filament.custom.report-timeline'),
                    ]),

                Section::make(__('Media'))
                    ->schema([
                        ViewEntry::make('report_media')
                            ->label('Attached Media')
                            ->view('filament.custom.report-media-gallery'),
                    ]),

                Section::make(__('Location'))
                    ->schema([
                        ViewEntry::make('location')
                            ->label('Map')
                            ->view('filament.custom.location-map'),
                    ]),
            ]);
    }

    #[Override]
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    #[Override]
    public static function getPages(): array
    {
        return [
            'index' => ListReports::route('/'),
            // 'create' => Pages\CreateReport::route('/create'),
            'view' => ViewReport::route('/{record}'),
            'timeline' => TimelineReport::route('/{record}/timeline'),
        ];
    }

    #[Override]
    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            ViewReport::class,
            TimelineReport::class,
        ]);
    }

    #[Override]
    public static function getPluralModelLabel(): string
    {
        return __('Reports');
    }

    #[Override]
    public static function getNavigationLabel(): string
    {
        return __('Reports');
    }

    public function getTitle(): string
    {
        return __('Reports');
    }
}
