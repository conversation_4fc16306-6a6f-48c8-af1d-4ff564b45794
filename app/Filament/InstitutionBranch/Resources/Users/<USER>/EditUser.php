<?php

declare(strict_types=1);

namespace App\Filament\InstitutionBranch\Resources\Users\Pages;

use App\Filament\InstitutionBranch\Resources\Users\UserResource;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;
use Override;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    #[Override]
    public function getTitle(): string
    {
        return __('Edit User');
    }

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
        ];
    }
}
