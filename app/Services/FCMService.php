<?php

declare(strict_types=1);

namespace App\Services;

use Exception;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\MessageData;
use Log;

class FCMService
{
    public function __construct(protected Messaging $messaging) {}

    /**
     * @throws FirebaseException
     * @throws MessagingException
     */
    public function sendFCMWithData(array $deviceToken, string $title, string $body, MessageData|array $data): void
    {
        // send notification
        $message = CloudMessage::new()
            ->withData($data)
            ->withNotification([
                'title' => $title,
                'body' => $body,
            ]);

        try {
            $this->messaging->sendMulticast($message, $deviceToken);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
