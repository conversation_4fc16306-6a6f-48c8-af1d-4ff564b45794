<?php

declare(strict_types=1);

namespace App\Actions;

use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class EshaarClient
{
    public string $requestId;

    public bool $is_verified = false;

    private readonly string $language;

    private readonly string $paymentMethod;

    private readonly int $expiry;

    private readonly string $senderName;

    private readonly int $length;

    private readonly string $apiKey;

    private readonly string $apiUrl;

    public function __construct()
    {
        $this->language = config('eshaar.otp_language');
        $this->paymentMethod = config('eshaar.otp_payment_method');
        $this->expiry = (int) config('eshaar.otp_expiry');
        $this->senderName = config('eshaar.otp_sender_name');
        $this->length = (int) config('eshaar.otp_length');
        $this->apiKey = config('eshaar.api_key');
        $this->apiUrl = config('eshaar.api_url');
    }

    /**
     * @throws ConnectionException
     */
    public function sendOTP(string $phone, ?string $sender = null): array
    {
        // /otp/initiate
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post($this->apiUrl.'/otp/initiate', [
            'receiver' => $phone,
            'lang' => $this->language,
            'length' => $this->length,
            'expiration' => $this->expiry,
            'sender' => $sender !== null && $sender !== '' && $sender !== '0' ? $sender : $this->senderName,
            'payment_type' => $this->paymentMethod,
        ]);

        $responseData = $this->handleResponse($response);

        // Store the requestId if present in the response
        if (isset($responseData['request_id'])) {
            $this->requestId = $responseData['request_id'];
        }

        return $responseData;
    }

    /**
     * Get the stored request ID from the last OTP send operation
     */
    public function getRequestId(): ?string
    {
        return $this->requestId ?? null;
    }

    /**
     * Get the verification status
     */
    public function getIsVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * @throws ConnectionException
     */
    public function verifyOtp(string $requestId, string $code): array
    {
        // /otp/verify
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->apiKey,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post($this->apiUrl.'/otp/verify', [
            'request_id' => $requestId,
            'code' => $code,
        ]);

        $responseData = $this->handleResponse($response);

        if ($response->status() === 200) {
            $this->is_verified = true;
        }

        return $responseData;
    }

    /**
     * Handle API response and throw appropriate exceptions
     *
     * @throws Exception
     */
    protected function handleResponse(Response $response): array
    {
        $data = $response->json();

        if ($response->successful()) {
            return $data;
        }

        // Handle different error types
        switch ($response->status()) {
            case 401:
                throw new Exception($response->json()['message'] ?? 'Unauthorized', 401);
            case 422:
                $errors = $data['errors'] ?? $data['message'] ?? 'Validation failed';
                throw new Exception('422 Validation error: '.json_encode($errors), 422);
            default:
                $message = $data['message'] ?? $response->status().' API request failed';
                throw new Exception($message, $response->status());
        }
    }
}
