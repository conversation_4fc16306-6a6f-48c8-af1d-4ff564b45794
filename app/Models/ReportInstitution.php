<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReportInstitution extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'report_id',
        'institution_id',
        'institution_branch_id',
        'status',
    ];

    public function report(): BelongsTo
    {
        return $this->belongsTo(Report::class);
    }

    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class);
    }

    public function institution_branch(): BelongsTo
    {
        return $this->belongsTo(InstitutionBranch::class);
    }
}
