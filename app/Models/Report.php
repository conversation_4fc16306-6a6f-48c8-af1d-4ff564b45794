<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ReportObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

#[ObservedBy([ReportObserver::class])]
class Report extends Model implements HasMedia
{
    use HasFactory, HasUuids ,InteractsWithMedia;
    use HasRelationships;

    protected $fillable = [
        'title',
        'location',
        'content',
        'resident_id',
        'type_id',
        'category_id',
        'municipality_id',
        'nearest_address',
        'code',
    ];

    protected $casts = [
        'location' => 'array',
    ];

    public function resident(): BelongsTo
    {
        return $this->belongsTo(Resident::class);
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(ReportType::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ReportCategory::class);
    }

    public function timelines(): HasMany
    {
        return $this->hasMany(ReportTimeline::class);
    }

    public function municipality(): BelongsTo
    {
        /** @var HasOneThrough<Municipality, MunicipalityBranch, $this> */
        return $this->belongsTo(Municipality::class);
    }
    /*public function municipalityBranch(): MunicipalityBranch|\Illuminate\Database\Eloquent\Builder
    {
        return MunicipalityBranch::whereIn('id', function ($query) {
            $query->select('municipality_branch_id')
                ->from('municipality_branch_institution_branch')
                ->whereIn('institution_branch_id', function ($subQuery) {
                    $subQuery->select('institution_branch_id')
                        ->from('report_institutions')
                        ->where('report_id', $this->id);
                });
        });
    }*/

    public function municipalityBranch(): HasManyDeep
    {
        return $this->hasManyDeep(
            MunicipalityBranch::class,
            [
                ReportInstitution::class,
                InstitutionBranch::class,
                MunicipalityBranchInstitutionBranch::class,
            ],
            [
                'report_id', // ReportInstitution
                'id',        // InstitutionBranch
                'institution_branch_id', // MunicipalityBranchInstitutionBranch
            ],
            [
                'id',        // Report
                'institution_branch_id', // ReportInstitution
                'id',         // InstitutionBranch
            ]
        );
    }

    public function institution()
    {
        return $this->belongsToMany(Institution::class, 'report_institutions');
    }

    public function institutionBranches(): BelongsToMany
    {
        return $this->belongsToMany(InstitutionBranch::class, 'report_institutions');
    }

    public function reportInstitutions(): HasMany
    {
        return $this->hasMany(ReportInstitution::class);
    }

    public function institutionBranch()
    {
        return $this->belongsToMany(InstitutionBranch::class, 'report_institutions');
    }

    public function lastTimeline()
    {
        return $this->timelines()->latest()->first();
    }

    public function scopeWhereLatestTimelineStatus(Builder $q, $status): Builder
    {
        return $q->whereHas('timelines', function (Builder $sub) use ($status): void {
            $sub->where('status', $status)
                ->whereRaw(
                    'report_timelines.created_at = (
                     select max(rt.created_at)
                     from report_timelines rt
                     where rt.report_id = report_timelines.report_id
                 )'
                );
        });
    }
}
