<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ReportTimeline extends Model
{
    use HasUuids;

    protected $fillable = [
        'status',
        'details',
        'notes',
        'report_id',
        'user_id',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    public function report(): BelongsTo
    {
        return $this->belongsTo(Report::class);
    }

    public function user(): MorphTo
    {
        return $this->morphTo();
    }
}
