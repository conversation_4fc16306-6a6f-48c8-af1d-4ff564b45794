<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ResidentsDeviceToken extends Model
{
    use HasUuids;

    protected $fillable = [
        'resident_id',
        'token',
        'last_seen',
    ];

    protected $casts = [
        'last_seen' => 'datetime',
    ];

    /** @return BelongsTo<Resident, $this> */
    public function resident(): BelongsTo
    {
        return $this->belongsTo(Resident::class);
    }
}
