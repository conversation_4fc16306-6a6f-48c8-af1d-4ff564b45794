<?php

declare(strict_types=1);

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Database\Factories\UserFactory;
use Exception;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;

class User extends Authenticatable implements FilamentUser, HasTenants
{
    /** @use HasFactory<UserFactory> */
    use HasFactory, HasUuids, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function municipalities(): BelongsToMany
    {
        return $this->belongsToMany(Municipality::class);
    }

    public function municipality(): BelongsToMany
    {
        return $this->belongsToMany(Municipality::class);
    }

    public function institutions(): BelongsToMany
    {
        return $this->belongsToMany(Institution::class);
    }

    public function institutionBranches(): BelongsToMany
    {
        return $this->belongsToMany(InstitutionBranch::class);
    }

    public function municipalityBranches(): BelongsToMany
    {
        return $this->belongsToMany(MunicipalityBranch::class);
    }

    public function municipalityBranch(): BelongsToMany
    {
        return $this->belongsToMany(municipalityBranch::class);
    }

    public function institutionBranch(): BelongsToMany
    {
        return $this->belongsToMany(institutionBranch::class);
    }

    public function institution(): BelongsToMany
    {
        return $this->BelongsToMany(Institution::class);
    }

    /**
     * @throws Exception
     */
    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin' && $this->is_admin) {
            return true;
        }

        return $this->getTenants($panel)->isNotEmpty();
    }

    public function canAccessTenant(Model $tenant): bool
    {
        if ($tenant instanceof Municipality) {
            return $this->municipalities->contains($tenant);
        }

        if ($tenant instanceof Institution) {
            return $this->institution->contains($tenant);
        }

        if ($tenant instanceof MunicipalityBranch) {
            return $this->municipalityBranch->contains($tenant);
        }

        if ($tenant instanceof InstitutionBranch) {
            return $this->institutionBranches->contains($tenant);
        }

        return false;
    }

    public function getTenants(Panel $panel): Collection
    {
        if ($panel->getId() === 'municipality') {
            return $this->municipalities;
        }

        if ($panel->getId() === 'institution') {
            return $this->institutions;
        }

        if ($panel->getId() === 'municipalityBranch') {
            return $this->municipalityBranches;
        }

        if ($panel->getId() === 'institutionBranch') {
            return $this->institutionBranches;
        }

        return collect();
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
