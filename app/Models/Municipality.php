<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Municipality extends Model implements HasMedia
{
    use HasFactory, HasUuids;
    use InteractsWithMedia;

    protected $fillable = [
        'name',
        'location',
        'phone',
        'status',
        'email',
        'website',
        'code',
    ];

    protected $casts = [
        'location' => 'array',
    ];

    public function branches(): HasMany
    {
        return $this->hasMany(MunicipalityBranch::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
}
