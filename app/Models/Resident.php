<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\HasApiTokens;

class Resident extends Authenticatable
{
    use HasApiTokens, HasFactory, HasUuids, Notifiable;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'password',
        'status',
        'municipality_branch_id',
        'verified_at',
    ];

    protected $casts = [
        'verified_at' => 'datetime',
    ];

    protected $hidden = [
        'password',
    ];

    /** @return BelongsTo<MunicipalityBranch, $this> */
    public function municipality_branch(): BelongsTo
    {
        return $this->belongsTo(MunicipalityBranch::class, 'municipality_branch_id');
    }

    /** @return HasOneThrough<Municipality, MunicipalityBranch, $this> */
    public function municipality(): HasOneThrough
    {
        return $this->hasOneThrough(
            Municipality::class,
            MunicipalityBranch::class,
            'id',
            'id',
            'municipality_branch_id',
            'municipality_id'
        );
    }

    /** @return HasMany<Report, $this> */
    public function reports(): HasMany
    {
        return $this->HasMany(Report::class, 'resident_id');
    }

    public function validateForApiPasswordGrant(string $password): bool
    {
        return Hash::check($password, $this->password);
    }

    public function deviceTokens(): HasMany
    {
        return $this->hasMany(ResidentsDeviceToken::class);
    }
}
