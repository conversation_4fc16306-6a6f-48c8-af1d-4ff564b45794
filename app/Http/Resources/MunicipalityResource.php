<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Municipality;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class MunicipalityResource extends JsonResource
{
    #[Override]
    public function toArray(Request $request): array
    {
        /** @var Municipality $municipality */
        $municipality = $this->resource;

        return [
            'id' => $municipality->id,
            'name' => $municipality->name,
            'phone' => $municipality->phone,
            'email' => $municipality->email,
            'status' => $municipality->status,
            'location' => $municipality->location,
            'image' => $municipality->getFirstMediaUrl('municipality_logo'),
        ];
    }
}
