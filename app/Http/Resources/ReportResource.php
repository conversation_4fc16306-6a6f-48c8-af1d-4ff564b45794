<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class ReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        /** @var Report $report */
        $report = $this->resource;

        return [
            'id' => $report->id,
            'code' => $report->code,
            'title' => $report->title,
            'content' => $report->content,
            'location' => $report->location,
            'type' => $report->type->name,
            'category' => $report->category->name,
            'created_at' => $report->created_at,
            'updated_at' => $report->updated_at,
            'last_status' => new TimeLineResource($report->timelines()->latest()->first()),
            'images' => $report->getMedia('report_media')->map(fn ($media): string => $media->getTemporaryUrl(now()->addMinutes(10))),
        ];
    }
}
