<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class TypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        $type = $this->resource;

        return [
            'id' => $type->id,
            'name' => $type->name,
            'icon' => $type->icon,
            'category' => $type->category->name,
        ];
    }
}
