<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Institution;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class InstitutionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        /** @var Institution $institution */
        $institution = $this->resource;

        $image = $institution->getMedia('institution_logo');

        return [
            'id' => $institution->id,
            'name' => $institution->name,
            'phone' => $institution->phone,
            'email' => $institution->email,
            'website' => $institution->website,
            'status' => $institution->status,
            'image' => $image->first() ? $image->first()->getTemporaryUrl(now()->addMinutes(10)) : '',
        ];
    }
}
