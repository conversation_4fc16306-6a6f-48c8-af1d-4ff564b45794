<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Resident;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class ResidentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        /** @var Resident $resident */
        $resident = $this->resource;

        return [
            'id' => $resident->id,
            'name' => $resident->name,
            'phone' => $resident->phone,
            'email' => $resident->email,
            'status' => $resident->status,
            'municipality_name' => $resident->municipality->name,
            'municipality_branch_name' => $resident->municipality_branch->name,
            'municipality_branch_id' => $resident->municipality_branch_id,
        ];
    }
}
