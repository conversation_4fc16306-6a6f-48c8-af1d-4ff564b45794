<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class ReportInstitutions extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        $report = $this->resource;

        return [
            'name' => $report->name,
            'location' => $report->location,
            'phone' => $report->phone,
            'email' => $report->email,
            'website' => $report->website,
        ];
    }
}
