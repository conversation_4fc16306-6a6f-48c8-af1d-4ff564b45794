<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\ReportTimeline;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class TimeLineResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        /** @var ReportTimeline $timeline */
        $timeline = $this->resource;

        return [
            'id' => $timeline->id,
            'status' => $timeline->status,
            'notes' => $timeline->notes,
            'created_at' => $timeline->created_at,
            'user' => $timeline->user->name ?? 'System',
        ];
    }
}
