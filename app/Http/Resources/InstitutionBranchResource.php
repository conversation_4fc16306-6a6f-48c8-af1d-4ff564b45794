<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class InstitutionBranchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    #[Override]
    public function toArray(Request $request): array
    {
        $institutionBranch = $this->resource;

        return [
            'id' => $institutionBranch->id,
            'name' => $institutionBranch->name,
            'phone' => $institutionBranch->phone,
            'status' => $institutionBranch->status,
        ];
    }
}
