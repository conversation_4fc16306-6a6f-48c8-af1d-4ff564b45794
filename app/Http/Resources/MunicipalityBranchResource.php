<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

class MunicipalityBranchResource extends JsonResource
{
    #[Override]
    public function toArray(Request $request): array
    {
        $branch = $this->resource;

        $image = $branch->getMedia('municipality_branch_logo');

        return [
            'id' => $branch->id,
            'name' => $branch->name,
            'phone' => $branch->phone,
            'email' => $branch->email,
            'status' => $branch->status,
            'location' => $branch->location,
            'image' => $image->first() ? $image->first()->getTemporaryUrl(now()->addMinutes(10)) : '',
        ];
    }
}
