<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class DeviceToken extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'access_token' => 'required|string',
        ];
    }

    /**
     * Get custom body parameters for Scribe documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'access_token' => [
                'description' => 'The device token for push notifications (FCM token).',
                'example' => 'dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU',
            ],
        ];
    }
}
