<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\PhoneNumberRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class Resend extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone' => ['required', 'starts_with:00218', new PhoneNumberRule()],
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'phone' => [
                'description' => 'The phone of the user.',
                'example' => '00218912345678',
            ],
        ];
    }
}
