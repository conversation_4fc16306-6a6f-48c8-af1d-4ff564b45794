<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\PhoneNumberRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class Register extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'email' => 'email',
            'phone' => ['required', 'starts_with:00218', 'unique:residents', new PhoneNumberRule()],
            'municipality_branch_id' => 'required|exists:municipality_branches,id',
            'password' => 'required|confirmed|min:8',
            'password_confirmation' => 'required',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'name' => [
                'description' => 'The name of the user.',
                'example' => 'Mohamed Ali',
            ],
            'email' => [
                'description' => 'The email of the user.',
                'example' => '<EMAIL>',
            ],
            'phone' => [
                'description' => 'The phone of the user.',
                'example' => '00218912345678',
            ],
            'municipality_branch_id' => [
                'description' => 'The <code>id</code> of an existing record in the municipality_branches table.',
                'example' => '01979743-adda-707e-a2c9-3242ebd67c60',
            ],
            'password' => [
                'description' => 'The password of the user.',
                'example' => ')kl$keB7T}w*19qh',
            ],
            'password_confirmation' => [
                'description' => 'The password confirmation of the user.',
                'example' => ')kl$keB7T}w*19qh',
            ],
        ];
    }
}
