<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\PhoneNumberRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class Login extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'phone' => ['required', 'starts_with:00218', new PhoneNumberRule()],
            'password' => 'required|string|min:8',
        ];
    }

    /**
     * Get custom body parameters for Scribe documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'phone' => [
                'description' => 'The phone number of the user (must start with 00218).',
                'example' => '00218912345678',
            ],
            'password' => [
                'description' => 'The password of the user.',
                'example' => 'secret123',
            ],
        ];
    }
}
