<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class Verify extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'request_id' => 'required',
            'code' => 'required',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'request_id' => [
                'description' => 'The request id.',
                'example' => '0197972a-1669-7157-a679-54784cd0002a',
            ],
            'code' => [
                'description' => 'The code.',
                'example' => '123456',
            ],
        ];
    }
}
