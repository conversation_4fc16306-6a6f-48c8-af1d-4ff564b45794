<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class SendReport extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'nearest_address' => 'required|string',
            'title' => 'required|string',
            'location.lat' => 'required|numeric|between:-90,90',
            'location.lng' => 'required|numeric|between:-180,180',
            'content' => 'required|string',
            'type_id' => 'required|exists:report_types,id',
            'category_id' => 'required|exists:report_categories,id',
            'municipality_id' => 'required|exists:municipalities,id',
            'media' => 'nullable|array|max:6',
            'media.*' => 'file|mimes:jpg,jpeg,png,webp,mp4,mov|max:2048',
        ];
    }

    public function bodyParameters(): array
    {
        return [
            'title' => [
                'description' => 'The title of the report.',
                'example' => 'Report title',
            ],
            'location' => [
                'description' => 'The location of the report.',
                'example' => [
                    'lat' => 32.3152506,
                    'lng' => 15.0148311,
                ],
            ],
            'content' => [
                'description' => 'The content of the report.',
                'example' => 'Report content',
            ],
            'type_id' => [
                'description' => 'The type of the report.',
                'example' => '01979743-adda-707e-a2c9-3242ebd67c60',
            ],
            'category_id' => [
                'description' => 'The category of the report.',
                'example' => '01979743-adda-707e-a2c9-3242ebd67c60',
            ],
            'municipality_id' => [
                'description' => 'The municipality of the report.',
                'example' => '01979743-adda-707e-a2c9-3242ebd67c60',
            ],
            'nearest_address' => [
                'description' => 'The nearest address of the report.',
                'example' => 'Nearest address',
            ],
        ];
    }
}
