<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    public function sendNotification(Request $request, FCMService $firebase)
    {
        $data = Validator::make([
            'tokenDevice' => $request['tokenDevice'],
            'title' => $request['title'],
            'body' => $request['body'],
        ], [
            'tokenDevice' => 'required|string',
            'title' => 'required',
            'body' => 'required',
        ])->validate();

        return $firebase->sendNotification($data['tokenDevice'], $data['title'], $data['body']);
    }
}
