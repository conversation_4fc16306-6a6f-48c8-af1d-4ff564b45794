<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\CategoryResource;
use App\Http\Resources\TypeResource;
use App\Models\ReportCategory;
use App\Models\ReportType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoryController extends Controller
{
    /**
     * Get report categories
     *
     * Retrieve all active report categories available for creating reports.
     * Categories help organize different types of municipal issues.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @response 200 scenario="Categories retrieved successfully" [
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c60",
     *     "name": "Infrastructure",
     *     "status": "active"
     *   },
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c61",
     *     "name": "Public Services",
     *     "status": "active"
     *   },
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c62",
     *     "name": "Environmental",
     *     "status": "active"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving categories."
     * }
     */
    public function categories()
    {
        $categories = ReportCategory::where('status', 'active')->get();

        return CategoryResource::collection($categories);
    }

    /**
     * Get report types by category
     *
     * Retrieve all active report types for a specific category. Report types provide
     * more specific classification within each category for better issue routing.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @urlParam id string required The UUID of the report category. Example: ********-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Report types retrieved successfully" [
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c70",
     *     "name": "Water & Sewage",
     *     "icon": "water-drop",
     *     "category": "Infrastructure"
     *   },
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c71",
     *     "name": "Road Maintenance",
     *     "icon": "road",
     *     "category": "Infrastructure"
     *   },
     *   {
     *     "id": "********-adda-707e-a2c9-3242ebd67c72",
     *     "name": "Street Lighting",
     *     "icon": "lightbulb",
     *     "category": "Infrastructure"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Category not found" {
     *   "message": "Category not found."
     * }
     * @response 422 scenario="Invalid category ID" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "id": ["The selected id is invalid."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving report types."
     * }
     */
    public function types(Request $request, string $id)
    {
        $data = Validator::make(
            [
                'id' => $request['id'],
            ],
            [
                'id' => 'required|exists:report_categories,id',
            ],
        )->validate();

        $types = ReportType::with('category')
            ->where('category_id', $data['id'])
            ->where('status', 'active')
            ->get();

        return TypeResource::collection($types);
    }
}
