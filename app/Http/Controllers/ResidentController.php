<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\ReportTimelineStatus;
use App\Http\Requests\SendReport;
use App\Http\Resources\ReportCollection;
use App\Http\Resources\ReportInstitutions;
use App\Http\Resources\ReportResource;
use App\Http\Resources\TimeLineResource;
use App\Models\Report;
use App\Models\Resident;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Throwable;

class ResidentController extends Controller
{
    /**
     * Create a new report
     *
     * Submit a new report from a resident to the municipality system. The report will be automatically
     * assigned to relevant institutions based on the report type and will create an initial timeline entry.
     *
     * @group Resident Reports
     *
     * @authenticated
     *
     * @bodyParam title string required The title of the report. Example: Water pipe burst on Main Street
     * @bodyParam content string required Detailed description of the issue. Example: A major water pipe has burst causing flooding and disrupting traffic flow.
     * @bodyParam location object required Geographic coordinates of the incident.
     * @bodyParam location.lat number required Latitude coordinate. Example: 32.3152506
     * @bodyParam location.lng number required Longitude coordinate. Example: 15.0148311
     * @bodyParam type_id string required UUID of the report type. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     * @bodyParam category_id string required UUID of the report category. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     * @bodyParam municipality_id string required UUID of the municipality. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     * @bodyParam nearest_address string required Nearest recognizable address or landmark. Example: Near City Hall, Main Street
     * @bodyParam media file[] optional Array of media files (images/videos). Maximum 6 files, each up to 2MB. Supported formats: jpg, jpeg, png, webp, mp4, mov.
     *
     * @response 200 scenario="Report created successfully" {
     *   "message": "report successfully created",
     *   "report": {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c60",
     *     "code": "M-001111",
     *     "title": "Water pipe burst on Main Street",
     *     "content": "A major water pipe has burst causing flooding and disrupting traffic flow.",
     *     "location": {
     *       "lat": 32.3152506,
     *       "lng": 15.0148311
     *     },
     *     "type": "Infrastructure",
     *     "category": "Water & Sewage"
     *   }
     * }
     * @response 400 scenario="Invalid request data" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "title": ["The title field is required."],
     *     "location": ["The location field is required."],
     *     "type_id": ["The selected type id is invalid."]
     *   }
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "media.0": ["The media.0 must be a file of type: jpg, jpeg, png, webp, mp4, mov."],
     *     "media": ["The media may not have more than 6 items."]
     *   }
     * }
     * @response 500 scenario="Server error during report creation" {
     *   "message": "Failed to create report"
     * }
     *
     * @throws Throwable
     */
    public function report_send(SendReport $request)
    {
        /** @var Resident $resident */
        $resident = $request->user();

        $validatedData = $request->validated();

        // convert location lat and lng to number
        $location = [
            'lat' => (float) $validatedData['location']['lat'],
            'lng' => (float) $validatedData['location']['lng'],
        ];

        $validatedData['location'] = $location;

        // remove media from validated data
        unset($validatedData['media']);

        try {
            $report = DB::transaction(function () use ($resident, $validatedData, $request) {
                $report = $resident->reports()->create($validatedData);

                $this->assignReportToInstitutions($report);

                // Add report activity to the timeline
                $report->timelines()->create([
                    'status' => ReportTimelineStatus::Created->value,
                    'details' => json_encode($validatedData),
                    'notes' => __('created by :name', ['name' => $resident->name]),
                    'user_type' => Resident::class,
                    'user_id' => $resident->id,
                ]);

                // Upload media to Spatie Media Library
                if ($request->hasFile('media')) {
                    foreach ($request->file('media') as $file) {
                        $report
                            ->addMedia($file)
                            ->toMediaCollection('report_media', 's3');
                    }
                }

                return $report;
            });
        } catch (Throwable $th) {
            Log::error($th->getMessage());

            return response()->json(['message' => 'Failed to create report'], 500);
        }

        return response()->json([
            'message' => 'report successfully created',
            'report' => new ReportResource($report),
        ]);
    }

    /**
     * Get report timeline
     *
     * Retrieve the complete timeline of activities and status changes for a specific report.
     * Only returns timeline for reports owned by the authenticated resident.
     *
     * @group Resident Reports
     *
     * @authenticated
     *
     * @urlParam id string required The UUID of the report. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Timeline retrieved successfully" [
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *     "status": "created",
     *     "notes": "created by John Doe",
     *     "created_at": "2024-01-15T10:30:00.000000Z",
     *     "user": "John Doe"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c62",
     *     "status": "assigned",
     *     "notes": "Assigned to Water Department",
     *     "created_at": "2024-01-15T11:00:00.000000Z",
     *     "user": "System Admin"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c63",
     *     "status": "in_progress",
     *     "notes": "Investigation started by field team",
     *     "created_at": "2024-01-15T14:30:00.000000Z",
     *     "user": "Field Supervisor"
     *   }
     * ]
     * @response 400 scenario="Invalid report ID format" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "id": ["The id field is required."]
     *   }
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Report not found or not owned by resident" {
     *   "message": "Report not found."
     * }
     * @response 422 scenario="Report ID validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "id": ["The selected id is invalid."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving timeline."
     * }
     */
    public function timeline(Request $request, string $id)
    {
        $data = Validator::make(
            [
                'id' => $request['id'],
            ],
            [
                'id' => 'required|exists:reports,id',
            ],
        )->validate();

        /** @var Resident $user */
        $user = auth()->user();

        $timeline = $user->reports()->find($data['id'])->timelines()->with('user')->get();

        return TimeLineResource::collection($timeline);
    }

    /**
     * Get resident's reports
     *
     * Retrieve a paginated list of all reports submitted by the authenticated resident.
     * Results are ordered by creation date (newest first) and include report type and category information.
     *
     * @group Resident Reports
     *
     * @authenticated
     *
     * @queryParam page integer optional Page number for pagination. Example: 1
     * @queryParam per_page integer optional Number of reports per page (default: 10). Example: 15
     *
     * @response 200 scenario="Reports retrieved successfully" {
     *   "reports": [
     *     {
     *       "id": "01979743-adda-707e-a2c9-3242ebd67c60",
     *       "code": "M-001111",
     *       "title": "Water pipe burst on Main Street",
     *       "content": "A major water pipe has burst causing flooding and disrupting traffic flow.",
     *       "location": {
     *         "lat": 32.3152506,
     *         "lng": 15.0148311
     *       },
     *       "type": "Infrastructure",
     *       "category": "Water & Sewage"
     *       "created_at": "2024-01-15T10:30:00.000000Z",
     *       "updated_at": "2024-01-15T10:30:00.000000Z"
     *       "last_status": {
     *         "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *         "status": "created",
     *         "notes": "created by John Doe",
     *         "created_at": "2024-01-15T10:30:00.000000Z",
     *         "user": "John Doe"
     *       }
     *       "images": [
     *         'https://s3.amazonaws.com/bucket/report-media/image1.jpg',
     *         'https://s3.amazonaws.com/bucket/report-media/video1.mp4'
     *       ]
     *     },
     *     {
     *       "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *       "title": "Broken streetlight on Oak Avenue",
     *       "content": "The streetlight has been out for several days creating safety concerns.",
     *       "location": {
     *         "lat": 32.3200000,
     *         "lng": 15.0200000
     *       },
     *       "type": "Infrastructure",
     *       "category": "Lighting"
     *       "created_at": "2024-01-15T10:30:00.000000Z",
     *       "updated_at": "2024-01-15T10:30:00.000000Z"
     *       "last_status": {
     *          "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *          "status": "created",
     *          "notes": "created by John Doe",
     *          "created_at": "2024-01-15T10:30:00.000000Z",
     *          "user": "John Doe"
     *        }
     *       "images": [
     *          'https://s3.amazonaws.com/bucket/report-media/image1.jpg',
     *          'https://s3.amazonaws.com/bucket/report-media/video1.mp4'
     *        ]
     *     }
     *   ],
     *   "links": {
     *     "first": "http://localhost/api/resident/reports?page=1",
     *     "last": "http://localhost/api/resident/reports?page=3",
     *     "prev": null,
     *     "next": "http://localhost/api/resident/reports?page=2"
     *   },
     *   "meta": {
     *     "current_page": 1,
     *     "from": 1,
     *     "last_page": 3,
     *     "per_page": 10,
     *     "to": 10,
     *     "total": 25
     *   }
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Invalid pagination parameters" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "page": ["The page must be an integer."],
     *     "per_page": ["The per page must be an integer."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving reports."
     * }
     */
    public function reports(Request $request)
    {
        $validated_data = Validator::make(
            [
                'page' => $request['page'],
                'per_page' => $request['per_page'],
            ],
            [
                'page' => 'nullable|integer',
                'per_page' => 'nullable|integer',
            ],
        )->validate();

        $user = auth()->user();

        $reports = Report::query()->with(['type', 'category']);

        $reports->where('resident_id', $user->id)->orderBy('created_at', 'desc');

        $reportsData = $reports->paginate(10 ?? $validated_data['per_page']);

        return response()->json(new ReportCollection($reportsData));
    }

    /**
     * Get report details
     *
     * Retrieve detailed information about a specific report owned by the authenticated resident.
     * Includes all report data including location, type, category, and content.
     *
     * @group Resident Reports
     *
     * @authenticated
     *
     * @urlParam id string required The UUID of the report. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Report details retrieved successfully" {
     *   "id": "01979743-adda-707e-a2c9-3242ebd67c60",
     *   "title": "Water pipe burst on Main Street",
     *   "content": "A major water pipe has burst causing flooding and disrupting traffic flow. The issue started around 8 AM and has affected multiple businesses in the area.",
     *   "location": {
     *     "lat": 32.3152506,
     *     "lng": 15.0148311
     *   },
     *   "type": "Infrastructure",
     *   "category": "Water & Sewage"
     *   "created_at": "2024-01-15T10:30:00.000000Z",
     *   "updated_at": "2024-01-15T10:30:00.000000Z"
     *   "last_status": {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *     "status": "created",
     *     "notes": "created by John Doe",
     *     "created_at": "2024-01-15T10:30:00.000000Z",
     *     "user": "John Doe"
     *   }
     *   "images": [
     *     'https://s3.amazonaws.com/bucket/report-media/image1.jpg',
     *     'https://s3.amazonaws.com/bucket/report-media/video1.mp4'
     *   ]
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Report not found or not owned by resident" {
     *   "message": "Report not found."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving report details."
     * }
     */
    public function report(Request $request, string $id)
    {
        $validated_data = Validator::make(
            [
                'id' => $id,
            ],
            [
                'id' => 'required|exists:reports,id',
            ],
        )->validate();

        /** @var Resident $resident */
        $resident = $request->user();

        $report = Report::where('id', $validated_data['id'])->where('resident_id', $resident->id)->first();

        return response()->json(new ReportResource($report));
    }

    /**
     * Get report assigned institutions
     *
     * Retrieve the list of institutions that have been assigned to handle a specific report.
     * Only returns institutions for reports owned by the authenticated resident.
     *
     * @group Resident Reports
     *
     * @authenticated
     *
     * @urlParam report_id string required The UUID of the report. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Institutions retrieved successfully" [
     *   {
     *     "name": "Municipal Water Department",
     *     "location": {
     *       "lat": 32.3152506,
     *       "lng": 15.0148311
     *     },
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "website": "https://water.municipality.ly"
     *   },
     *   {
     *     "name": "Emergency Response Unit",
     *     "location": {
     *       "lat": 32.3200000,
     *       "lng": 15.0200000
     *     },
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "website": "https://emergency.municipality.ly"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Report not found or not owned by resident" {
     *   "message": "Report not found."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving institutions."
     * }
     */
    public function reports_institutions(Request $request, string $report_id)
    {
        $validated_data = Validator::make(
            [
                'report_id' => $report_id,
            ],
            [
                'report_id' => 'required|exists:reports,id',
            ],
        )->validate();

        $reports = Report::query()
            ->with(['institution'])
            ->where('resident_id', auth()->user()->id)
            ->where('id', $validated_data['report_id'])->first();

        $institutions = $reports->institution;

        return ReportInstitutions::collection($institutions);
    }

    private function assignReportToInstitutions(Report $report): void
    {
        $report->type->Institution->each(function ($institution) use ($report): void {
            $report->reportInstitutions()->create(
                [
                    'institution_id' => $institution->id,
                    'status' => ReportTimelineStatus::Assigned->value,
                ]
            );
        });
    }
}
