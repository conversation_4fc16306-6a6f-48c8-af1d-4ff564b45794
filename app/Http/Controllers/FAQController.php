<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\FAQResource;
use App\Models\FAQ;

class FAQ<PERSON>ontroller extends Controller
{
    /**
     * Get frequently asked questions
     *
     * Retrieve all active frequently asked questions to help residents
     * understand the system and find answers to common queries.
     *
     * @group Resident Data
     *
     * @unauthenticated
     *
     * @response 200 scenario="FAQs retrieved successfully" {
     * "data": [
     *    {
     *      "question": "How do I submit a report?",
     *      "answer": "You can submit a report by logging into your account, navigating to the reports section, and filling out the report form with details about the issue you want to report."
     *    },
     *    {
     *      "question": "How long does it take to process a report?",
     *      "answer": "Report processing time varies depending on the type and urgency of the issue. Typically, reports are reviewed within 24-48 hours and assigned to the appropriate department."
     *    },
     *    {
     *      "question": "Can I track the status of my report?",
     *      "answer": "Yes, you can track your report status by viewing the timeline in your reports section. You'll see all updates and actions taken on your report."
     *    },
     *    {
     *      "question": "What types of issues can I report?",
     *      "answer": "You can report various municipal issues including infrastructure problems, public service concerns, environmental issues, and other matters that fall under municipal jurisdiction."
     *    }
     *  ]
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving FAQs."
     * }
     */
    public function index()
    {
        $faqs = FAQ::where('is_active', true)->get();

        return FAQResource::collection($faqs);
    }
}
