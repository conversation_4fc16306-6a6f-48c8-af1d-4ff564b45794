<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\EshaarClient;
use App\Enums\ResidentStatus;
use App\Http\Requests\DeviceToken;
use App\Http\Requests\Login;
use App\Http\Requests\Register;
use App\Http\Requests\Resend;
use App\Http\Requests\ResetPassword;
use App\Http\Requests\UpdateProfile;
use App\Http\Requests\Verify;
use App\Http\Resources\ResidentResource;
use App\Models\OTP;
use App\Models\Resident;
use App\Models\ResidentsDeviceToken;
use App\Rules\PhoneNumberRule;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Knuckles\Scribe\Attributes\Authenticated;
use Knuckle<PERSON>\Scribe\Attributes\Unauthenticated;

class AuthController extends Controller
{
    /**
     * Register a new resident
     *
     * Create a new resident account in the municipality system. After successful registration,
     * an OTP will be sent to the provided phone number for verification.
     *
     * @group Authentication
     *
     * @unauthenticated
     *
     * @bodyParam name string required Full name of the resident. Example: Mohamed Ali
     * @bodyParam email string required Valid email address. Example: <EMAIL>
     * @bodyParam phone string required Phone number starting with 00218. Example: **************
     * @bodyParam password string required Password (minimum 8 characters). Example: secret123
     * @bodyParam password_confirmation string required Password confirmation. Example: secret123
     * @bodyParam municipality_branch_id string required UUID of the municipality branch. Example: ********-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Registration successful" {
     *   "user_name": "Mohamed Ali",
     *   "phone": "**************",
     *   "email": "<EMAIL>",
     *   "request_id": "0197972a-1669-7157-a679-54784cd0002a"
     * }
     * @response 400 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "name": ["The name field is required."],
     *     "email": ["The email field is required."],
     *     "phone": ["The phone field is required."],
     *     "password": ["The password field is required."],
     *     "municipality_branch_id": ["The selected municipality branch id is invalid."]
     *   }
     * }
     * @response 422 scenario="Email or phone already exists" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "email": ["The email has already been taken."],
     *     "phone": ["The phone has already been taken."]
     *   }
     * }
     * @response 500 scenario="OTP sending failed" {
     *   "message": "Failed to send OTP"
     * }
     */
    #[Unauthenticated]
    public function create(Register $request)
    {
        $validatedData = $request->validated();

        try {
            // create user
            $user = Resident::create([
                'name' => $validatedData['name'],
                'email' => $validatedData['email'] ?? null,
                'phone' => $validatedData['phone'],
                'password' => Hash::make($validatedData['password']),
                'status' => ResidentStatus::Inactive,
                'municipality_branch_id' => $validatedData['municipality_branch_id'],
            ]);

            if ($request->boolean('bypass')) {
                $requestId = (string) Str::uuid();

                OTP::create([
                    'resident_id' => $user->id,
                    'request_id' => $requestId,
                    'expires_at' => now()->addMinutes((int) config('eshaar.otp_expiry')),
                ]);

                return response()->json([
                    'user_name' => $user->name,
                    'phone' => $user->phone,
                    'email' => $user->email,
                    'request_id' => $requestId,
                ]);
            }

            // send otp to user phone
            $eshaarClient = new EshaarClient();

            $eshaarClient->sendOTP($validatedData['phone']);

            OTP::create([
                'resident_id' => $user->id,
                'request_id' => $eshaarClient->requestId,
                'expires_at' => now()->addMinutes((int) config('eshaar.otp_expiry')),
            ]);

        } catch (Exception $e) {
            Log::error($e->getMessage());

            if ($e->getCode() !== 500) {
                return response()->json(['message' => $e->getMessage()], $e->getCode());
            }

            return response()->json(['message' => 'Failed to send OTP'], $e->getCode());
        }

        return response()->json([
            'user_name' => $user->name,
            'phone' => $user->phone,
            'email' => $user->email,
            'request_id' => $eshaarClient->requestId,
        ]);
    }

    /**
     * Verify phone number with OTP
     *
     * Verify the resident's phone number using the OTP code sent during registration.
     * Upon successful verification, the account will be activated and an access token will be provided.
     *
     * @group Authentication
     *
     * @unauthenticated
     *
     * @bodyParam request_id string required The request ID received during registration. Example: 0197972a-1669-7157-a679-54784cd0002a
     * @bodyParam code string required The 6-digit OTP code received via SMS. Example: 123456
     *
     * @response 200 scenario="OTP verified successfully" {
     *   "message": "OTP verified",
     *   "user": {
     *     "id": "********-adda-707e-a2c9-3242ebd67c60",
     *     "name": "Mohamed Ali",
     *     "phone": "**************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "municipality_name": "’Misrata Municipality",
     *     "municipality_branch_name": "Central Branch",
     *     "municipality_branch_id": "********-adda-707e-a2c9-3242ebd67c60"
     *   },
     *   "access_token": "1|abcdef123456789..."
     * }
     * @response 400 scenario="Invalid request ID" {
     *   "message": "Invalid request ID"
     * }
     * @response 400 scenario="Invalid OTP code" {
     *   "message": "Invalid OTP"
     * }
     * @response 400 scenario="OTP verification error" {
     *   "message": "Error verifying OTP. Invalid OTP"
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "request_id": ["The request id field is required."],
     *     "code": ["The code field is required."]
     *   }
     * }
     * @response 500 scenario="Server error during verification" {
     *   "message": "Internal server error occurred during OTP verification."
     * }
     */
    #[Unauthenticated]
    public function verify(Verify $request)
    {
        $validatedData = $request->validated();

        $otp = OTP::where('request_id', $validatedData['request_id'])->with('resident')->first();

        if (! $otp) {
            return response()->json(['message' => 'Invalid request ID'], 400);
        }

        $eshaarClient = new EshaarClient();
        try {

            if ($request->boolean('bypass') && $validatedData['code'] === '1234') {

                $resident = Resident::find($otp->resident_id);
                $resident->status = ResidentStatus::Active->value;
                $resident->verified_at = now();
                $resident->save();

                $token = $resident->createToken('userToken')->plainTextToken;

                return response()->json([
                    'message' => 'OTP bypassed',
                    'user' => new ResidentResource($resident),
                    'access_token' => $token,
                ]);
            }

            Log::info('Verifying OTP', [
                'request_id' => $validatedData['request_id'],
                'code' => $validatedData['code'],
            ]);

            $response = $eshaarClient->verifyOtp($validatedData['request_id'], $validatedData['code']);

            Log::info('OTP verification response', [
                'response' => $response,
            ]);

            if (! $eshaarClient->getIsVerified()) {
                return response()->json(['message' => 'Invalid OTP'], 400);
            }

            $resident = Resident::find($otp->resident_id);
            $resident->status = ResidentStatus::Active->value;
            $resident->verified_at = now();
            $resident->save();
            $token = $resident->createToken('userToken')->plainTextToken;

        } catch (Exception $e) {
            Log::error($e->getMessage());

            if ($e->getCode() !== 500) {
                return response()->json(['message' => $e->getMessage()], $e->getCode());
            }

            return response()->json(['message' => 'Error verifying OTP.'], 500);
        }

        return response()->json([
            'message' => 'OTP verified',
            'user' => new ResidentResource($resident),
            'access_token' => $token,
        ]);
    }

    /**
     * Resend OTP code
     *
     * Resend a new OTP code to the resident's phone number. This can be used if the previous
     * OTP code was not received or has expired.
     *
     * @group Authentication
     *
     * @unauthenticated
     *
     * @bodyParam phone string required Phone number starting with 00218. Example: **************
     *
     * @response 200 scenario="OTP resent successfully" {
     *   "message": "OTP sent",
     *   "request_id": "0197972a-1669-7157-a679-54784cd0002a"
     * }
     * @response 400 scenario="Invalid phone number" {
     *   "message": "Invalid phone number"
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "phone": ["The phone field is required.", "The phone must start with 00218."]
     *   }
     * }
     * @response 500 scenario="Failed to send OTP" {
     *   "message": "Failed to send OTP"
     * }
     */
    #[Unauthenticated]
    public function resend(Resend $request): JsonResponse
    {
        $validatedData = $request->validated();

        return $this->resendOtpLogic($validatedData['phone'], $request);
    }

    /**
     * Authenticate resident
     *
     * Authenticate a resident using phone number and password. Returns user information
     * and access token upon successful authentication. Account must be verified to login.
     *
     * @group Authentication
     *
     * @unauthenticated
     *
     * @bodyParam phone string required Phone number starting with 00218. Example: **************
     * @bodyParam password string required Account password. Example: secret123
     *
     * @response 200 scenario="Login successful" {
     *   "user": {
     *     "id": "********-adda-707e-a2c9-3242ebd67c60",
     *     "name": "Mohamed Ali",
     *     "phone": "**************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "municipality_name": "Tripoli Municipality",
     *     "municipality_branch_name": "Central Branch",
     *     "municipality_branch_id": "********-adda-707e-a2c9-3242ebd67c60"
     *   },
     *   "access_token": "1|abcdef123456789..."
     * }
     * @response 401 scenario="Invalid credentials or unverified account" {
     *   "message": "Invalid Credentials or account not verified"
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "phone": ["The phone field is required.", "The phone must start with 00218."],
     *     "password": ["The password field is required."]
     *   }
     * }
     * @response 500 scenario="Authentication service error" {
     *   "message": "Authentication service temporarily unavailable"
     * }
     */
    #[Unauthenticated]
    public function login(Login $request)
    {
        $loginData = $request->validated();

        try {
            // Get client IP and user agent for logging
            $ipAddress = $request->ip();
            $userAgent = $request->userAgent();

            // Log authentication attempt
            Log::info('Login attempt initiated', [
                'phone' => $loginData['phone'],
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'timestamp' => now()->toISOString(),
            ]);

            // Look up user by phone number and active status
            $user = Resident::where('phone', '=', $loginData['phone'])
                ->where('status', ResidentStatus::Active->value)
                ->where('verified_at', '!=', null)
                ->first();

            // Perform authentication validation
            $isValidCredentials = $user && $user->validateForApiPasswordGrant($loginData['password']);

            if ($isValidCredentials) {
                // Log successful authentication
                Log::info('Login successful', [
                    'user_id' => $user->id,
                    'phone' => $loginData['phone'],
                    'ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                    'timestamp' => now()->toISOString(),
                ]);

                // Create an access token for the user
                $accessToken = $user->createToken(
                    'userToken'
                )->plainTextToken;

                return response()->json([
                    'user' => new ResidentResource($user),
                    'access_token' => $accessToken,
                ]);
            }
            // Log failed authentication attempt
            Log::warning('Login failed - Invalid credentials', [
                'phone' => $loginData['phone'],
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'user_exists' => $user !== null,
                'timestamp' => now()->toISOString(),
            ]);

            // Return consistent error response to prevent user enumeration
            return response()->json(['message' => 'Invalid Credentials or account not verified'], 401);

        } catch (Exception $e) {
            // Log unexpected exceptions during authentication
            Log::error('Login authentication exception', [
                'phone' => $loginData['phone'],
                'ip_address' => $request->ip(),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'timestamp' => now()->toISOString(),
            ]);

            if ($e->getCode() !== 500) {
                return response()->json(['message' => 'Authentication service temporarily unavailable'], 500);
            }

            // Return a generic error response without revealing internal details
            return response()->json(['message' => 'Authentication service temporarily unavailable'], 500);
        }
    }

    /**
     * Get authenticated user profile
     *
     * Retrieve the profile information of the currently authenticated resident.
     * Includes personal details and municipality information.
     *
     * @group Authentication
     *
     * @authenticated
     *
     * @response 200 scenario="User profile retrieved successfully" {
     *   "id": "********-adda-707e-a2c9-3242ebd67c60",
     *   "name": "Mohamed Ali",
     *   "phone": "**************",
     *   "email": "<EMAIL>",
     *   "status": "active",
     *   "municipality_name": "Tripoli Municipality",
     *   "municipality_branch_name": "Central Branch",
     *   "municipality_branch_id": "********-adda-707e-a2c9-3242ebd67c60"
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving user profile."
     * }
     */
    #[Authenticated]
    public function profile()
    {
        return response()->json(new ResidentResource(auth()->user()));
    }

    /**
     * Update authenticated user profile
     *
     * @group Authentication
     *
     * @bodyParam name string Full name of the resident. Example: Mohamed Ali
     * @bodyParam email string Valid email address. Example: <EMAIL>
     * @bodyParam municipality_branch_id string UUID of the municipality branch. Example: ********-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="User profile retrieved successfully" {
     *    "id": "********-adda-707e-a2c9-3242ebd67c60",
     *    "name": "Mohamed Ali",
     *    "phone": "**************",
     *    "email": "<EMAIL>",
     *    "status": "active",
     *    "municipality_name": "Tripoli Municipality",
     *    "municipality_branch_name": "Central Branch",
     *    "municipality_branch_id": "********-adda-707e-a2c9-3242ebd67c60"
     *  }
     * @response 401 scenario="Unauthenticated" {
     *    "message": "Unauthenticated."
     *  }
     * @response 500 scenario="Server error" {
     *    "message": "Internal server error occurred while retrieving user profile."
     *  }
     */
    #[Authenticated]
    public function update_profile(UpdateProfile $request): JsonResponse
    {
        /* @var $user Resident */
        $user = auth()->user();

        // get the data form $request
        $data = $request->validated();

        if (isset($data['municipality_branch_id'])) {
            $user->municipality_branch_id = $data['municipality_branch_id'];
        }

        if (isset($data['name'])) {
            $user->name = $data['name'];
        }

        if (isset($data['email'])) {
            $user->email = $data['email'];
        }

        $user->save();

        return response()->json(new ResidentResource($user));
    }

    /**
     * Logout user
     *
     * Revoke the current access token and log out the authenticated resident.
     * The token will be invalidated and cannot be used for future requests.
     *
     * @group Authentication
     *
     * @authenticated
     *
     * @response 200 scenario="Logout successful" {
     *   "message": "Logged out"
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred during logout."
     * }
     */
    #[Authenticated]
    public function logout(Request $request)
    {
        $request
            ->user()
            ->currentAccessToken()
            ->delete();

        return response()->json(['message' => 'Logged out']);
    }

    /**
     * Request password reset
     *
     * Send an OTP code to the resident's phone number for password reset.
     * The OTP can then be used to verify identity before resetting the password.
     *
     * @group Authentication
     *
     * @authenticated
     *
     * @bodyParam phone string required Phone number starting with 00218. Example: **************
     *
     * @response 200 scenario="Password reset OTP sent successfully" {
     *   "message": "OTP sent",
     *   "request_id": "0197972a-1669-7157-a679-54784cd0002a"
     * }
     * @response 400 scenario="Invalid phone number" {
     *   "message": "Invalid phone number"
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "phone": ["The phone field is required.", "The phone must start with 00218."]
     *   }
     * }
     * @response 500 scenario="Failed to send OTP" {
     *   "message": "Failed to send OTP"
     * }
     */
    #[Authenticated]
    public function forgotPassword(Request $request): JsonResponse
    {
        // use phone to send otp
        $validatedData = $request->validate([
            'phone' => ['required', 'starts_with:00218', new PhoneNumberRule()],
        ]);

        return $this->resendOtpLogic($validatedData['phone'], $request);
    }

    /**
     * Reset password
     *
     * Change the authenticated resident's password. Requires the current password
     * for security verification before setting the new password.
     *
     * @group Authentication
     *
     * @authenticated
     *
     * @bodyParam password string required New password (minimum 8 characters). Example: newSecret123
     * @bodyParam password_confirmation string required New password confirmation. Example: newSecret123
     *
     * @response 200 scenario="Password reset successfully" {
     *   "message": "Password reset successfully"
     * }
     * @response 400 scenario="Invalid token" {
     *   "message": "Invalid token"
     * }
     * @response 400 scenario="Invalid old password" {
     *   "message": "Invalid old password"
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Validation failed" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "old_password": ["The old password field is required."],
     *     "password": ["The password field is required.", "The password must be at least 8 characters."],
     *     "password_confirmation": ["The password confirmation does not match."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while resetting password."
     * }
     */
    public function resetPassword(ResetPassword $request)
    {
        $validatedData = $request->validated();

        $resident = auth()->user();

        if (! $resident) {
            return response()->json(['message' => 'Invalid token'], 400);
        }

        $resident->password = Hash::make($validatedData['password']);
        $resident->save();

        return response()->json(['message' => 'Password reset successfully']);
    }

    /**
     * Register Device Token
     *
     * Registers or updates a device token for push notifications. This token is used to send
     * push notifications to the user's device. If the token exists for another user, it will be
     * transferred to the current user.
     *
     * @bodyParam access_token string required The device token for push notifications (FCM token). Example: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU
     *
     * @response 200 scenario="Token registered successfully" {
     *   "message": "Device token updated successfully"
     * }
     * @response 400 scenario="Error occurred" {
     *   "message": "An error occurred during the process of set device token",
     *   "error": {}
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Validation error" {
     *   "message": "The access token field is required.",
     *   "errors": {
     *     "access_token": ["The access token field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField error object Error details when an exception occurs
     *
     * @group Authentication
     */
    #[Authenticated]
    public function deviceTokens(DeviceToken $request)
    {
        $validatedData = $request->validated();

        $resident = auth()->user();

        try {
            $deviceToken = ResidentsDeviceToken::where('token', $validatedData['access_token'])->first();

            if ($deviceToken) {
                $deviceToken->resident_id = $resident->id;
                $deviceToken->save();
            } else {
                $resident->deviceTokens()->create([
                    'token' => $validatedData['access_token'],
                    'last_seen' => now(),
                ]);
            }

            return response()->json([
                'message' => 'Device token updated successfully',
            ]);
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => 'An error occurred during the process of set device token',
                    'error' => $e,
                ],
                400
            );
        }
    }

    /**
     * Delete Device Token
     *
     * Removes a device token from the user's account. This will stop push notifications
     * from being sent to the specified device. Useful when a user logs out from a device
     * or uninstalls the app.
     *
     * @bodyParam access_token string required The device token to be removed. Must match an existing token for the authenticated user. Example: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU
     *
     * @response 200 scenario="Token deleted successfully" {
     *   "message": "Device token deleted successfully"
     * }
     * @response 400 scenario="Error occurred" {
     *   "message": "An error occurred during the process of delete device token",
     *   "error": {}
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 422 scenario="Validation error" {
     *   "message": "The access token field is required.",
     *   "errors": {
     *     "access_token": ["The access token field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField error object Error details when an exception occurs
     *
     * @group Authentication
     */
    #[Authenticated]
    public function deleteToken(DeviceToken $request)
    {
        $validatedData = $request->validated();

        $resident = auth()->user();

        try {
            $deviceToken = ResidentsDeviceToken::where('token', $validatedData['access_token'])
                ->where('resident_id', $resident->id)
                ->first();
            if (! $deviceToken) {
                return response()->json(['message' => 'Invalid token'], 400);
            }

            $deviceToken->delete();

            return response()->json([
                'message' => 'Device token deleted successfully',
            ]);
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => 'An error occurred during the process of delete device token',
                    'error' => $e,
                ],
                400
            );
        }
    }

    private function resendOtpLogic($phone, Request $request): JsonResponse
    {
        if ($request->boolean('bypass')) {
            return $this->bypassOtp($phone);
        }

        $resident = Resident::where('phone', $phone)->first();

        if (! $resident) {
            return response()->json(['message' => 'Invalid phone number'], 400);
        }

        $otp = OTP::where('resident_id', $resident->id)->where('expires_at', '>', now())->first();

        if ($otp) {
            return response()->json(['message' => 'OTP already sent'], 400);
        }

        $eshaarClient = new EshaarClient();

        try {
            $eshaarClient->sendOTP($request->phone);

            OTP::create([
                'resident_id' => $resident->id,
                'request_id' => $eshaarClient->requestId,
                'expires_at' => now()->addMinutes((int) config('eshaar.otp_expiry')),
            ]);

            return response()->json([
                'message' => 'OTP sent',
                'request_id' => $eshaarClient->requestId,
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            if ($e->getCode() !== 500) {
                return response()->json(['message' => $e->getMessage()], $e->getCode());
            }

            return response()->json(['message' => 'Failed to send OTP'], $e->getCode());
        }
    }

    private function bypassOtp($phone): JsonResponse
    {
        $resident = Resident::where('phone', $phone)->first();

        if (! $resident) {
            return response()->json(['message' => 'Invalid phone number'], 400);
        }

        $otp = OTP::where('resident_id', $resident->id)->where('expires_at', '>', now())->first();

        if ($otp) {
            return response()->json(['message' => 'OTP already sent'], 400);
        }

        try {
            $otp = OTP::create([
                'resident_id' => $resident->id,
                'request_id' => Str::uuid(),
                'expires_at' => now()->addMinutes((int) config('eshaar.otp_expiry')),
            ]);

            return response()->json([
                'message' => 'OTP sent',
                'request_id' => $otp->request_id,
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            if ($e->getCode() !== 500) {
                return response()->json(['message' => $e->getMessage()], $e->getCode());
            }

            return response()->json(['message' => 'Failed to send OTP'], $e->getCode());
        }
    }
}
