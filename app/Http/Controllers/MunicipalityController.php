<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\MunicipalityBranchResource;
use App\Http\Resources\MunicipalityResource;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Unauthenticated;

class MunicipalityController extends Controller
{
    /**
     * Get municipality branches
     *
     * Retrieve all active branches for a specific municipality. Branches represent
     * different service locations within a municipality where residents can access services.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @urlParam id string required The UUID of the municipality. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Municipality branches retrieved successfully" [
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c80",
     *     "name": "Central Branch",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "location": {
     *       "lat": 32.3152506,
     *       "lng": 15.0148311
     *     },
     *     "logo": "https://s3.amazonaws.com/bucket/logo.png"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c81",
     *     "name": "Eastern Branch",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "location": {
     *       "lat": 32.3200000,
     *       "lng": 15.0200000
     *     },
     *     "logo": "https://s3.amazonaws.com/bucket/logo2.png"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Municipality not found" {
     *   "message": "Municipality not found."
     * }
     * @response 422 scenario="Invalid municipality ID" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "id": ["The selected id is invalid."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving municipality branches."
     * }
     */
    #[Unauthenticated]
    public function branches(Request $request, string $id)
    {
        $data = Validator::make(
            [
                'id' => $request['id'],
            ],
            [
                'id' => 'required|exists:municipalities,id',
            ],
        )->validate();

        $branches = MunicipalityBranch::where('municipality_id', $data['id'])->where('status', 'active')->get();

        return MunicipalityBranchResource::collection($branches);
    }

    /**
     * Get all municipalities
     *
     * Retrieve all active municipalities in the system. Municipalities are the main
     * administrative divisions that residents can register with and submit reports to.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @response 200 scenario="Municipalities retrieved successfully" [
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c60",
     *     "name": "Tripoli Municipality",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "location": {
     *       "lat": 32.3152506,
     *       "lng": 15.0148311
     *     },
     *     "image": "https://s3.amazonaws.com/bucket/tripoli-logo.png"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c61",
     *     "name": "Benghazi Municipality",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "status": "active",
     *     "location": {
     *       "lat": 32.1167,
     *       "lng": 20.0667
     *     },
     *     "image": "https://s3.amazonaws.com/bucket/benghazi-logo.png"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving municipalities."
     * }
     */
    #[Unauthenticated]
    public function municipality()
    {
        $municipality = Municipality::where('status', 'active')->get();

        return MunicipalityResource::collection($municipality);
    }
}
