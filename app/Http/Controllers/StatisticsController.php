<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\ReportTimelineStatus;
use App\Models\Report;
use App\Models\Resident;
use Illuminate\Http\Request;

class StatisticsController extends Controller
{
    /**
     * Get resolved reports count by municipality
     *
     * Retrieve the total number of distinct reports that have been marked as resolved
     * within the municipality of the authenticated resident.
     *
     * @group Resolved Reports
     *
     * @authenticated
     *
     * @response 200 scenario="Resolved reports count retrieved successfully" {
     *   "total": 42
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving municipality report count."
     * }
     */
    public function municipality(Request $request)
    {
        /** @var Resident $user */
        $user = auth()->user();
        $municipality = $user->municipality;

        if (! $municipality) {
            return response()->json([
                'total' => 0,
                'error' => 'resident not linked to any municipality.',
            ]);
        }

        $total = Report::query()
            ->join('report_timelines', 'reports.id', '=', 'report_timelines.report_id')
            ->where('report_timelines.status', ReportTimelineStatus::Resolved->value)
            ->where('reports.municipality_id', $municipality->id)
            ->distinct((array) 'reports.id')
            ->count('reports.id');

        return response()->json([
            'total' => $total,
        ]);
    }

    /**
     * Get resolved reports count by resident
     *
     * Retrieve the total number of distinct reports submitted by the authenticated
     * resident that have been marked as resolved.
     *
     * @group Resolved Reports
     *
     * @authenticated
     *
     * @response 200 scenario="Resolved reports count retrieved successfully" {
     *   "total": 7
     * }
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving resident report count."
     * }
     */
    public function resident(Request $request)
    {
        /** @var Resident $user */
        $user = auth()->user();

        $total = Report::query()
            ->join('report_timelines', 'reports.id', '=', 'report_timelines.report_id')
            ->where('report_timelines.status', ReportTimelineStatus::Resolved->value)
            ->where('reports.resident_id', $user->id)
            ->distinct((array) 'reports.id')
            ->count('reports.id');

        return response()->json([
            'total' => $total,
        ]);
    }
}
