<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\InstitutionBranchResource;
use App\Http\Resources\InstitutionResource;
use App\Models\Institution;
use App\Models\InstitutionBranch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InstitutionController extends Controller
{
    /**
     * Get all institutions
     *
     * Retrieve all active institutions in the system. Institutions are government
     * departments and agencies responsible for handling different types of reports.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @response 200 scenario="Institutions retrieved successfully" [
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c90",
     *     "name": "Municipal Water Department",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "website": "https://water.municipality.ly",
     *     "status": "active",
     *     "image": "https://s3.amazonaws.com/bucket/water-dept-logo.png"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c91",
     *     "name": "Public Works Department",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "website": "https://works.municipality.ly",
     *     "status": "active",
     *     "image": "https://s3.amazonaws.com/bucket/works-dept-logo.png"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67c92",
     *     "name": "Environmental Protection Agency",
     *     "phone": "+************",
     *     "email": "<EMAIL>",
     *     "website": "https://environment.municipality.ly",
     *     "status": "active",
     *     "image": "https://s3.amazonaws.com/bucket/env-logo.png"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving institutions."
     * }
     */
    public function institutions()
    {
        $institutions = Institution::where('status', 'active')->get();

        return InstitutionResource::collection($institutions);
    }

    /**
     * Get institution branches
     *
     * Retrieve all active branches for a specific institution. Branches represent
     * different service locations or departments within an institution.
     *
     * @group Resident Data
     *
     * @authenticated
     *
     * @urlParam id string required The UUID of the institution. Example: 01979743-adda-707e-a2c9-3242ebd67c60
     *
     * @response 200 scenario="Institution branches retrieved successfully" [
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67ca0",
     *     "name": "Central Water Services",
     *     "phone": "+************",
     *     "status": "active"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67ca1",
     *     "name": "Eastern Water Services",
     *     "phone": "+************",
     *     "status": "active"
     *   },
     *   {
     *     "id": "01979743-adda-707e-a2c9-3242ebd67ca2",
     *     "name": "Emergency Response Unit",
     *     "phone": "+************",
     *     "status": "active"
     *   }
     * ]
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     * @response 404 scenario="Institution not found" {
     *   "message": "Institution not found."
     * }
     * @response 422 scenario="Invalid institution ID" {
     *   "message": "The given data was invalid.",
     *   "errors": {
     *     "id": ["The selected id is invalid."]
     *   }
     * }
     * @response 500 scenario="Server error" {
     *   "message": "Internal server error occurred while retrieving institution branches."
     * }
     */
    public function branches(Request $request, string $id)
    {
        $data = Validator::make(
            [
                'id' => $request['id'],
            ],
            [
                'id' => 'required|exists:institutions,id',
            ],
        )->validate();

        $branches = InstitutionBranch::where('institution_id', $data['id'])->where('status', 'active')->get();

        return InstitutionBranchResource::collection($branches);
    }
}
