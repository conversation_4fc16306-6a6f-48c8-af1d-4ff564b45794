<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\OTP;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BypassOTP
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // remove bypass if send by user
        $request->request->remove('bypass');

        $whiteList = $this->allowlisted();
        $requestPhone = $request->string('phone')->toString();

        if ($requestPhone !== '' && in_array($requestPhone, $whiteList, true)) {
            $request->merge(['bypass' => true]);
        }

        $requestId = $request->string('request_id')->toString();
        if ($requestId !== '') {
            $otp = OTP::where('request_id', $requestId)
                ->with('resident')
                ->first();

            $residentPhone = $otp?->resident?->phone;
            if ($residentPhone && in_array($residentPhone, $whiteList, true)) {
                $request->merge(['bypass' => true]);
            }
        }

        return $next($request);
    }

    public function allowlisted(): array
    {
        return [
            '00218910000000',
            '00218910000001',
            '00218910000002',
            '00218910000003',
        ];
    }
}
