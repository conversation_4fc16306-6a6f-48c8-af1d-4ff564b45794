# Municipality Management System

This is a system for managing municipalities, their branches, institutions, and users.

## Features
-   Municipalities
-   Municipality Branches
-   Institutions
-   Institution Branches
-   Users
-   Residents
-   Reports
-   Report Categories
-   Report Types
-   FAQ
-   Statistics
-   API Documentation
-   Multi-Language Support
-   Multi-Tenancy
-   Audit Logs
-   Activity Logs
-   Notifications
-   Email Verification

## Requirements
- PHP (version 8.x recommended)
- Composer
- Node.js & npm
- SQLite or your preferred database

## Installation
1. Clone the repository
2. Run `composer install`
3. Run `npm install`
4. Copy the `.env.example` file to `.env` and update the database credentials
5. Run `php artisan key:generate`
6. Run `php artisan migrate --seed`
7. Run `npm run dev`
8. Run `php artisan serve`

## Acknowledgements
-   [<PERSON><PERSON>](https://laravel.com)
-   [Filament](https://filamentphp.com)
-   [Livewire](https://livewire.com)
-   [Alpine.js](https://alpinejs.dev)
