[supervisord]
logfile=/dev/null
loglevel=info
nodaemon=true
pidfile=/tmp/supervisord.pid

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[unix_http_server]
file=/tmp/supervisor.sock   ; path to your socket file
chmod=0700                  ; socket file mode (default 0700)

; FrankenPHP web server
[program:web]
command=frankenphp php-server --root=/app/public --listen=0.0.0.0:8080
directory=/app
user=www-data
autostart=true
autorestart=true
startretries=999
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0

; Laravel queue worker
[program:queue]
command=php artisan queue:work --tries=3 --timeout=90 --sleep=1
directory=/app
user=www-data
autostart=true
autorestart=true
startretries=999
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0

; <PERSON><PERSON> scheduler (runs scheduled tasks continuously)
[program:schedule]
command=php artisan schedule:work
directory=/app
user=www-data
autostart=true
autorestart=true
startretries=999
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0

; Nightwatch agent
; [program:nightwatch]
; command=php artisan nightwatch:agent
; directory=/app
; user=www-data
; autostart=true
; autorestart=true
; startretries=999
; redirect_stderr=true
; stdout_logfile=/dev/fd/1
; stdout_logfile_maxbytes=0
