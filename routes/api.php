<?php

declare(strict_types=1);

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FAQController;
use App\Http\Controllers\InstitutionController;
use App\Http\Controllers\MunicipalityController;
use App\Http\Controllers\ResidentController;
use App\Http\Controllers\StatisticsController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'resident'], function () {

    Route::middleware('bypass_otp')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'create']);
        Route::post('verify', [AuthController::class, 'verify']);
        Route::post('resend', [AuthController::class, 'resend']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    });

    Route::get('faq', [FAQController::class, 'index']);
    Route::get('municipality', [MunicipalityController::class, 'municipality']);
    Route::get('municipality/{id}/branches', [MunicipalityController::class, 'branches']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/device-tokens', [AuthController::class, 'deviceTokens']);
        Route::post('/delete-device-tokens', [
            AuthController::class,
            'deleteToken',
        ]);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::post('profile', [AuthController::class, 'update_profile']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::get('report/{id}', [ResidentController::class, 'report']);
        Route::get('report/{id}/timeline', [ResidentController::class, 'timeline']);
        Route::get('report/{report_id}/institutions', [ResidentController::class, 'reports_institutions']);
        Route::post('reports/send', [ResidentController::class, 'report_send']);
        Route::get('reports', [ResidentController::class, 'reports']);

        Route::get('reports/categories', [CategoryController::class, 'categories']);
        Route::get('reports/categories/{id}/types', [CategoryController::class, 'types']);

        Route::get('institutions', [InstitutionController::class, 'institutions']);
        Route::get('institution/{id}/branches', [InstitutionController::class, 'branches']);

        Route::get('/statistic/municipality', [StatisticsController::class, 'municipality']);
        Route::get('/statistic', [StatisticsController::class, 'resident']);
    });
});
