<?php

declare(strict_types=1);

// use App\Http\Controllers\NotificationController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// firebase
// Route::get('/send-fcm', [NotificationController::class, 'sendNotification']);
// Route::get('/token', function () {
//    return view('getToken');
// });

/*Route::get('/test', function () {
    $report = \App\Models\Report::find('0197a70a-3d5c-7219-aeaf-82925dfcef82');

    dd($report->municipalityBranch()->count());
});*/

// create a small test to s3 upload and download
/*Route::get('/test', function () {
    Storage::disk('s3')->put('test.txt', 'Hello MinIO');

    $content = Storage::disk('s3')->get('test.txt');

    return $content;
});*/
