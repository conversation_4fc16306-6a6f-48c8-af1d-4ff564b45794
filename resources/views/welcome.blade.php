<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Municipality</title>

    <!-- Fonts -->
    <link href="{{ asset('fonts/font.css') }}" rel="stylesheet" />

    <style>
        body {
            font-family: 'sst-arabic', serif;
        }
    </style>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://unpkg.com/lucide@latest"></script>
</head>
<body>
    <div class="min-h-screen bg-gray-50 flex flex-col">
        <div class="flex flex-col items-center justify-center text-center py-10">
            <h1 class="text-3xl font-bold p-3">@lang('Municipality Management System')</h1>
            <p class="text-lg text-gray-600">@lang('Welcome to the Municipality Management System')</p>
            <p class="text-lg text-gray-600">@lang('Please select the module you want to access')</p>
        </div>

        <div class="grid grid-cols-3 sm:grid-cols-3 gap-6 p-8 max-w-xl mx-auto">
            <!-- Admin Panel -->
            <a href="/admin"
               class="flex flex-col items-center p-4 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                <i data-lucide="settings" class="w-8 h-8 text-blue-600"></i>
                <span class="mt-2 text-sm font-semibold">@lang('Admin Panel')</span>
            </a>

            <!-- Municipality Panel -->
            <a href="/municipality"
               class="flex flex-col items-center p-4 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                <i data-lucide="building-2" class="w-8 h-8 text-green-600"></i>
                <span class="mt-2 text-sm font-semibold">@lang('Municipality')</span>
            </a>

            <!-- Municipality Branch Panel -->
            <a href="/municipalityBranch"
               class="flex flex-col items-center p-4 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                <i data-lucide="git-branch" class="w-8 h-8 text-yellow-600"></i>
                <span class="mt-2 text-sm font-semibold text-center">@lang('Municipality Branch')</span>
            </a>

            <!-- Institution Panel -->
            <a href="/institution"
               class="flex flex-col items-center p-4 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                <i data-lucide="library" class="w-8 h-8 text-purple-600"></i>
                <span class="mt-2 text-sm font-semibold">@lang('Institution')</span>
            </a>

            <!-- Institution Branch Panel -->
            <a href="/institutionBranch"
               class="flex flex-col items-center p-4 bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                <i data-lucide="git-branch" class="w-8 h-8 text-pink-600"></i>
                <span class="mt-2 text-sm font-semibold text-center">@lang('Institution Branch')</span>
            </a>
        </div>

        <script>
            lucide.createIcons();
        </script>
    </div>
</body>
</html>
