@php
    $mediaItems = $getRecord()->getMedia('report_media');
@endphp

<div x-data="{ showModal: false, imageSrc: '' }">
    <div class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-6 gap-4">
        @forelse ($mediaItems as $media)
            <div class="cursor-pointer border rounded-lg overflow-hidden shadow-sm" @click="showModal = true; imageSrc = '{{ $media->getTemporaryUrl(now()->addMinutes(10)) }}'">
                <img
                    src="{{ $media->getTemporaryUrl(now()->addMinutes(10)) }}"
                    alt="{{ $media->name }}"
                    class="w-full h-28 object-cover"
                >
                <div class="p-2 text-xs text-gray-600 truncate">
                    {{ $media->name }}
                </div>
            </div>
        @empty
            <p class="text-sm text-gray-500">{{ __('No media attached.') }}</p>
        @endforelse
    </div>

    <!-- Modal -->
    <div
        x-show="showModal"
        x-transition
        x-cloak
        class="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center"
        @click.self="showModal = false"
    >
        <img :src="imageSrc" class="max-w-full max-h-full rounded shadow-lg">
        <button
            class="absolute top-4 right-4 text-white text-2xl"
            @click="showModal = false"
        >
            &times;
        </button>
    </div>
</div>
