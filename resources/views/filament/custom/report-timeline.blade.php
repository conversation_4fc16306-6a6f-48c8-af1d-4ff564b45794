@php
    $report = $getRecord();
    $timelines = \App\Models\ReportTimeline::where('report_id', $report->id)
        ->with('user')
        ->orderBy('created_at', 'desc')
        ->get();
@endphp

<div class="space-y-6">
    @foreach ($timelines as $entry)
        <div class="flex items-start space-x-4 border border-gray-200 p-3 rounded-lg">
            <div class="mt-1 shrink-0 me-2 border rounded-full overflow-hidden">
                @php
                $src = "https://ui-avatars.com/api/?name=".($entry->user->name ?? 'System') . "&color=FFFFFF&background=09090b"
                @endphp
                <x-filament::avatar
                    :src="$src"
                    :circle="true"
                    size="md"
                />
            </div>
            <div class="flex-1">
                {{--stsatus color--}}
                @php
                    $color = match ($entry->status) {
                        'assigned' => 'bg-gray-100 text-gray-800 border-gray-200',
                        'resolved' => 'bg-success-100 text-success-800 border-success-200',
                        'rejected' => 'bg-danger-100 text-danger-800 border-danger-200',
                        'retract' => 'bg-warning-100 text-warning-800 border-warning-200',
                        default => 'bg-primary-100 text-primary-800 border-primary-200',
                    };
                @endphp
                <div class="flex items-center text-sm gap-2 text-gray-600">
                    <p class="px-2 {{$color}} rounded border">@lang($entry->status)</p>
                    <p class="">@lang('by')</p>
                    <p class="font-medium text-gray-900">{{ $entry->user->name ?? 'System' }}</p>
                    <p class="text-md text-gray-400">· {{ $entry->created_at->diffForHumans() }}</p>
                </div>

                @if($entry->notes || $entry->details)
                    <div class="mt-4 text-sm text-gray-800 ">
                        @if($entry->notes)
                            <p class="font-medium">{{ $entry->notes }}</p>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    @endforeach
</div>
