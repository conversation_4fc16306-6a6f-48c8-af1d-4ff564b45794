# Statistics API

This document describes the resident statistics endpoints implemented in `StatisticsController`.

All endpoints below are prefixed with `resident/` and require authentication via `auth:sanctum` unless stated otherwise.

## Municipality Resolved Reports

- Method: `GET`
- Path: `/resident/statistic/municipality`
- Auth: Required (Sanctum)
- Query params: None

Returns the total number of distinct reports belonging to the authenticated resident’s municipality that have at least one timeline entry with status `resolved`.

### Success Response

200 OK

```json
{
  "total": 42
}
```

If the resident is not linked to any municipality, the endpoint still returns 200 with a zero total and an informational error message:

```json
{
  "total": 0,
  "error": "resident not linked to any municipality."
}
```

### Error Responses

- 401 Unauthorized

```json
{
  "message": "Unauthenticated."
}
```

- 500 Internal Server Error (generic)

```json
{
  "message": "Internal server error occurred while retrieving municipality report count."
}
```

### Example (cURL)

```bash
curl -X GET \
  -H "Authorization: Bearer <token>" \
  -H "Accept: application/json" \
  https://<host>/api/resident/statistic/municipality
```

## Resident Resolved Reports

- Method: `GET`
- Path: `/resident/statistic`
- Auth: Required (Sanctum)
- Query params: None

Returns the total number of distinct reports submitted by the authenticated resident that have at least one timeline entry with status `resolved`.

### Success Response

200 OK

```json
{
  "total": 7
}
```

### Error Responses

- 401 Unauthorized

```json
{
  "message": "Unauthenticated."
}
```

- 500 Internal Server Error (generic)

```json
{
  "message": "Internal server error occurred while retrieving resident report count."
}
```

### Example (cURL)

```bash
curl -X GET \
  -H "Authorization: Bearer <token>" \
  -H "Accept: application/json" \
  https://<host>/api/resident/statistic
```

## Notes

- Resolved definition: a report is counted if it has a `report_timelines` entry with status `resolved` (enum `ReportTimelineStatus::Resolved`). The current implementation counts distinct report IDs that have ever been resolved, regardless of newer statuses.
- Data scope: municipality counts are scoped by the authenticated resident’s municipality; resident counts are scoped by the authenticated resident’s own reports.

