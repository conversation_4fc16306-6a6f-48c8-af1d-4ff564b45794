<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Zain&display=swap" rel="stylesheet">
    <title>Municipality Report Flow</title>
    <style>
        body {
            font-family: 'Zain', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .flow-section {
            margin: 40px 0;
        }
        .entity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .entity-card {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        .entity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .entity-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .entity-card p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .municipality {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .institution {
            background: #e8f5e9;
            border-color: #4caf50;
        }
        .citizen {
            background: #fff3e0;
            border-color: #ff9800;
        }
        .report {
            background: #f3e5f5;
            border-color: #9c27b0;
        }
        .flow-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }
        .flow-step {
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px 30px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .flow-step::after {
            content: '↓';
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #007bff;
        }
        .flow-step:last-child::after {
            display: none;
        }
        .flow-step h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .flow-step ul {
            text-align: left;
            margin: 10px 0;
            padding-left: 20px;
        }
        .flow-step li {
            margin: 5px 0;
            font-size: 14px;
        }
        .branch-flow {
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        .branch {
            flex: 1;
            min-width: 300px;
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 20px;
        }
        .branch h4 {
            text-align: center;
            color: #495057;
            margin-bottom: 15px;
        }
        .arrow {
            font-size: 30px;
            color: #28a745;
            text-align: center;
            margin: 20px 0;
        }
        .status-timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            overflow-x: auto;
        }
        .status-node {
            flex: 1;
            text-align: center;
            position: relative;
            min-width: 120px;
        }
        .status-node::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #dee2e6;
        }
        .status-node:last-child::after {
            display: none;
        }
        .status-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            border: 3px solid #dee2e6;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        .status-node.active .status-circle {
            background: #28a745;
            border-color: #28a745;
            color: white;
        }
        .status-node.rejected .status-circle {
            background: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        .database-schema {
            margin: 40px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        .relationship {
            display: inline-block;
            padding: 3px 8px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 12px;
            margin: 0 2px;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 0 2px;
        }
        .badge-primary { background: #007bff; color: white; }
        .badge-success { background: #28a745; color: white; }
        .badge-danger { background: #dc3545; color: white; }
        .badge-warning { background: #ffc107; color: black; }
        .badge-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Municipality Report Management System - Flow Diagram</h1>

        <div class="flow-section">
            <h2>System Entities & Relationships</h2>
            <div class="entity-grid">
                <div class="entity-card municipality">
                    <h3>🏛️ Municipality</h3>
                    <p><strong>Example:</strong> بلدية مصراتة</p>
                    <p>Main municipal authority</p>
                    <p>Has multiple branches</p>
                </div>
                <div class="entity-card municipality">
                    <h3>🏢 Municipality Branch</h3>
                    <p><strong>Example:</strong> فرع الزروق</p>
                    <p>Local branch office</p>
                    <p>Serves specific area</p>
                </div>
                <div class="entity-card institution">
                    <h3>🏭 Institution</h3>
                    <p><strong>Example:</strong> شركة النظافة</p>
                    <p>Service provider</p>
                    <p>Handles specific report types</p>
                </div>
                <div class="entity-card institution">
                    <h3>🏗️ Institution Branch</h3>
                    <p><strong>Example:</strong> فرع النظافة - الزروق</p>
                    <p>Local service branch</p>
                    <p>Linked to municipality branches</p>
                </div>
                <div class="entity-card citizen">
                    <h3>👤 Citizen/Resident</h3>
                    <p>Registered users</p>
                    <p>Belongs to municipality branch</p>
                    <p>Submits reports</p>
                </div>
                <div class="entity-card report">
                    <h3>📋 Report</h3>
                    <p>Complaint/Request</p>
                    <p>Has category & type</p>
                    <p>Tracked through timeline</p>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>Report Submission Flow</h2>
            <div class="flow-diagram">
                <div class="flow-step">
                    <h4>1. Citizen Registration</h4>
                    <ul>
                        <li>Enter name, email, phone</li>
                        <li>Select municipality branch</li>
                        <li>Create password</li>
                        <li>Verify phone via SMS OTP</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>2. Login & Authentication</h4>
                    <ul>
                        <li>Login with phone + password</li>
                        <li>Access citizen dashboard</li>
                        <li>View previous reports</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>3. Create New Report</h4>
                    <ul>
                        <li>Select report category (e.g., الشكاوى)</li>
                        <li>Select report type (e.g., انقطاع تيار)</li>
                        <li>Select municipality (e.g. بلدية مصراتة) </li>
                        <li>Enter title and description</li>
                        <li>Add location</li>
                        <li>Submit report</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>4. Report Processing</h4>
                    <ul>
                        <li>Report received by municipality</li>
                        <li>Admin assigns to institution(s)</li>
                        <li>Institution branch receives task</li>
                        <li>Status updates tracked</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>5. Resolution</h4>
                    <ul>
                        <li>Institution processes report</li>
                        <li>Updates status to resolved/rejected</li>
                        <li>Citizen receives notification</li>
                        <li>Can view timeline history</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>Entity Connection Flow</h2>
            <div class="branch-flow">
                <div class="branch">
                    <h4>Municipality Side</h4>
                    <p><strong>Municipality:</strong> بلدية مصراتة</p>
                    <p>↓</p>
                    <p><strong>Branch:</strong> فرع الزروق</p>
                    <p>↓</p>
                    <p><strong>Citizens:</strong> Registered residents</p>
                </div>

                <div class="arrow">⟷</div>

                <div class="branch">
                    <h4>Institution Side</h4>
                    <p><strong>Institution:</strong> شركة النظافة</p>
                    <p>↓</p>
                    <p><strong>Branch:</strong> فرع النظافة - الزروق</p>
                    <p>↓</p>
                    <p><strong>Services:</strong> Handle reports</p>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>Report Status Timeline</h2>
            <div class="status-timeline">
                <div class="status-node active">
                    <div class="status-circle">1</div>
                    <p><strong>Created</strong></p>
                    <p>Report submitted</p>
                </div>
                <div class="status-node active">
                    <div class="status-circle">2</div>
                    <p><strong>Assigned</strong></p>
                    <p>To institution</p>
                </div>
                <div class="status-node">
                    <div class="status-circle">3</div>
                    <p><strong>In Progress</strong></p>
                    <p>Being processed</p>
                </div>
                <div class="status-node">
                    <div class="status-circle">4</div>
                    <p><strong>Resolved</strong></p>
                    <p>Completed</p>
                </div>
                <div class="status-node rejected">
                    <div class="status-circle">X</div>
                    <p><strong>Rejected</strong></p>
                    <p>Not applicable</p>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>Database Schema Overview</h2>
            <div class="database-schema">
                <table>
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Key Fields</th>
                            <th>Relationships</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>municipalities</strong></td>
                            <td>id, name, location, status</td>
                            <td><span class="relationship">HasMany → branches</span></td>
                            <td>Main municipal entities</td>
                        </tr>
                        <tr>
                            <td><strong>municipality_branches</strong></td>
                            <td>id, municipality_id, name, location</td>
                            <td>
                                <span class="relationship">BelongsTo → municipality</span>
                                <span class="relationship">HasMany → residents</span>
                            </td>
                            <td>Local branch offices</td>
                        </tr>
                        <tr>
                            <td><strong>institutions</strong></td>
                            <td>id, name, location, status</td>
                            <td><span class="relationship">HasMany → branches</span></td>
                            <td>Service provider organizations</td>
                        </tr>
                        <tr>
                            <td><strong>institution_branches</strong></td>
                            <td>id, institution_id, name, location</td>
                            <td>
                                <span class="relationship">BelongsTo → institution</span>
                                <span class="relationship">ManyToMany → municipality_branches</span>
                            </td>
                            <td>Local service branches</td>
                        </tr>
                        <tr>
                            <td><strong>residents</strong></td>
                            <td>id, name, phone, email, municipality_branch_id</td>
                            <td>
                                <span class="relationship">BelongsTo → municipality_branch</span>
                                <span class="relationship">HasMany → reports</span>
                            </td>
                            <td>System users (citizens)</td>
                        </tr>
                        <tr>
                            <td><strong>reports</strong></td>
                            <td>id, title, content, location, resident_id, type_id</td>
                            <td>
                                <span class="relationship">BelongsTo → resident</span>
                                <span class="relationship">ManyToMany → institutions</span>
                                <span class="relationship">HasMany → timelines</span>
                            </td>
                            <td>Citizen complaints/requests</td>
                        </tr>
                        <tr>
                            <td><strong>report_institutions</strong></td>
                            <td>report_id, institution_id, institution_branch_id, status</td>
                            <td>
                                <span class="relationship">Pivot table</span>
                            </td>
                            <td>Links reports to assigned institutions</td>
                        </tr>
                        <tr>
                            <td><strong>report_timelines</strong></td>
                            <td>id, report_id, status, user_id, notes</td>
                            <td>
                                <span class="relationship">BelongsTo → report</span>
                                <span class="relationship">MorphTo → user</span>
                            </td>
                            <td>Tracks report status changes</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="flow-section">
            <h2>Example Report Flow</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h3>Scenario: Citizen reports a garbage collection issue</h3>
                <ol>
                    <li><strong>Citizen (أحمد)</strong> from <span class="badge badge-primary">فرع الزروق</span> logs into mobile app</li>
                    <li>Creates new report:
                        <ul>
                            <li>Category: <span class="badge badge-info">الشكاوى</span></li>
                            <li>Type: <span class="badge badge-warning">مشكلة جمع القمامة</span></li>
                            <li>Title: "عدم جمع القمامة منذ أسبوع"</li>
                            <li>Location: GPS coordinates attached</li>
                        </ul>
                    </li>
                    <li>Report automatically creates timeline entry: <span class="badge badge-success">Created</span></li>
                    <li>Report automatically assigned to <span class="badge badge-primary">شركة النظافة</span> based on their category.</li>
                    <li><strong>Municipality Admin</strong> reviews and can assigns to other institutions if needed.</li>
                    <li>Timeline updated: <span class="badge badge-info">Assigned</span> to شركة النظافة - فرع الزروق</li>
                    <li><strong>Institution Branch</strong> receives notification and starts work</li>
                    <li>Timeline updated: <span class="badge badge-warning">In Progress</span></li>
                    <li>After completion, timeline updated: <span class="badge badge-success">Resolved</span></li>
                    <li>Citizen receives notification and can rate the service</li>
                </ol>
            </div>
        </div>

        <div class="flow-section">
            <h2>Multi-Panel Access Structure</h2>
            <div class="entity-grid">
                <div class="entity-card" style="background: #f8d7da; border-color: #dc3545;">
                    <h3>🔐 Admin Panel</h3>
                    <p><strong>URL:</strong> /admin</p>
                    <p><strong>Access:</strong> Super Admins</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>Manage all entities</li>
                        <li>Create users</li>
                        <li>View all reports</li>
                        <li>System configuration</li>
                    </ul>
                </div>

                <div class="entity-card municipality">
                    <h3>🏛️ Municipality Panel</h3>
                    <p><strong>URL:</strong> /municipality/{id}</p>
                    <p><strong>Access:</strong> Municipality Staff</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>Manage branches</li>
                        <li>View area reports</li>
                        <li>Assign to institutions</li>
                        <li>Generate statistics</li>
                    </ul>
                </div>

                <div class="entity-card municipality">
                    <h3>🏢 Municipality Branch Panel</h3>
                    <p><strong>URL:</strong> /municipalityBranch/{id}</p>
                    <p><strong>Access:</strong> Branch Staff</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>Process local reports</li>
                        <li>Manage residents</li>
                        <li>Coordinate with institutions</li>
                    </ul>
                </div>

                <div class="entity-card institution">
                    <h3>🏭 Institution Panel</h3>
                    <p><strong>URL:</strong> /institution/{id}</p>
                    <p><strong>Access:</strong> Institution Staff</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>View assigned reports</li>
                        <li>Manage branches</li>
                        <li>Track performance</li>
                    </ul>
                </div>

                <div class="entity-card institution">
                    <h3>🏗️ Institution Branch Panel</h3>
                    <p><strong>URL:</strong> /institutionBranch/{id}</p>
                    <p><strong>Access:</strong> Branch Staff</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>Process assigned reports</li>
                        <li>Update status</li>
                        <li>Add notes to timeline</li>
                    </ul>
                </div>

                <div class="entity-card citizen">
                    <h3>📱 Citizen Mobile App</h3>
                    <p><strong>Access:</strong> API Endpoints</p>
                    <p><strong>Auth:</strong> SMS OTP</p>
                    <ul style="text-align: left; font-size: 14px;">
                        <li>Submit reports</li>
                        <li>Track status</li>
                        <li>View timeline</li>
                        <li>Rate services</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>Key Features by User Type</h2>
            <table>
                <thead>
                    <tr>
                        <th>User Type</th>
                        <th>Key Features</th>
                        <th>Permissions</th>
                        <th>Access Level</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Super Admin</strong></td>
                        <td>
                            • Full system control<br>
                            • User management<br>
                            • Entity creation<br>
                            • Report oversight
                        </td>
                        <td>
                            <span class="badge badge-danger">All Permissions</span>
                        </td>
                        <td>System-wide</td>
                    </tr>
                    <tr>
                        <td><strong>Municipality Admin</strong></td>
                        <td>
                            • Branch management<br>
                            • Report assignment<br>
                            • Institution coordination<br>
                            • Area statistics
                        </td>
                        <td>
                            <span class="badge badge-primary">Municipality Scope</span>
                        </td>
                        <td>Municipality + Branches</td>
                    </tr>
                    <tr>
                        <td><strong>Institution Admin</strong></td>
                        <td>
                            • Branch oversight<br>
                            • Service management<br>
                            • Report processing<br>
                            • Performance tracking
                        </td>
                        <td>
                            <span class="badge badge-success">Institution Scope</span>
                        </td>
                        <td>Institution + Branches</td>
                    </tr>
                    <tr>
                        <td><strong>Branch Staff</strong></td>
                        <td>
                            • Local report handling<br>
                            • Status updates<br>
                            • Timeline management<br>
                            • Resident support
                        </td>
                        <td>
                            <span class="badge badge-info">Branch Scope</span>
                        </td>
                        <td>Branch only</td>
                    </tr>
                    <tr>
                        <td><strong>Citizen/Resident</strong></td>
                        <td>
                            • Report submission<br>
                            • Status tracking<br>
                            • Timeline viewing<br>
                            • Service rating
                        </td>
                        <td>
                            <span class="badge badge-warning">Own Data Only</span>
                        </td>
                        <td>Personal reports</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="flow-section">
            <h2>Report Type Configuration</h2>
            <div style="background: #e8f5e9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3>How Report Types Connect to Institutions</h3>
                <p>Each report type is linked to specific institutions that can handle it:</p>
                <div style="display: flex; align-items: center; justify-content: space-around; margin: 20px 0;">
                    <div style="text-align: center;">
                        <strong>Report Type</strong><br>
                        <span class="badge badge-info">انقطاع تيار</span>
                    </div>
                    <div>→</div>
                    <div style="text-align: center;">
                        <strong>Can be handled by</strong><br>
                        <span class="badge badge-success">شركة الكهرباء</span><br>
                        <span class="badge badge-success">شركة الصيانة</span>
                    </div>
                </div>
            </div>

            <h3>Branch Linking System</h3>
            <div style="background: #f0f8ff; padding: 20px; border-radius: 8px;">
                <p><strong>Municipality branches are linked to institution branches for local coordination:</strong></p>
                <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: center; margin-top: 20px;">
                    <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                        <strong>Municipality Branch</strong><br>
                        <span style="font-size: 18px;">فرع الزروق</span><br>
                        <small>Serves local area</small>
                    </div>
                    <div style="font-size: 30px;">↔️</div>
                    <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                        <strong>Institution Branches</strong><br>
                        <span style="font-size: 14px;">• شركة النظافة - فرع الزروق</span><br>
                        <span style="font-size: 14px;">• شركة المياه - فرع الزروق</span><br>
                        <small>Provide local services</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2>API Integration for Mobile App</h2>
            <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                <h3>Citizen App Flow</h3>
                <ol>
                    <li><strong>Registration:</strong> POST /api/resident/register
                        <ul>
                            <li>Provide: name, email, phone, password, municipality_branch_id</li>
                            <li>Receive: request_id for OTP verification</li>
                        </ul>
                    </li>
                    <li><strong>OTP Verification:</strong> POST /api/resident/verify
                        <ul>
                            <li>Provide: request_id, OTP code</li>
                            <li>Receive: access_token</li>
                        </ul>
                    </li>
                    <li><strong>Submit Report:</strong> POST /api/resident/reports/send
                        <ul>
                            <li>Headers: Authorization: Bearer {token}</li>
                            <li>Body: title, content, type_id, category_id, location</li>
                        </ul>
                    </li>
                    <li><strong>Track Report:</strong> GET /api/resident/report/{id}/timeline
                        <ul>
                            <li>View all status changes</li>
                            <li>See assigned institutions</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
