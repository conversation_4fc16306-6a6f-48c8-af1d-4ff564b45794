<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Municipality;
use App\Models\Report;
use App\Models\ReportCategory;
use App\Models\ReportType;
use App\Models\Resident;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Report> */
class ReportFactory extends Factory
{
    use FactoryHelpers;

    protected $model = Report::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->word(),
            'location' => $this->generateLocation($this->faker),
            'content' => $this->faker->word(),
            'resident_id' => Resident::factory(),
            'type_id' => ReportType::factory(),
            'category_id' => ReportCategory::factory(),
            'municipality_id' => Municipality::first()->id,
            'nearest_address' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
