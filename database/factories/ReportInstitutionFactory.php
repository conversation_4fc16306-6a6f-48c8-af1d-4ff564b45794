<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ReportInstitution;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ReportInstitutionFactory extends Factory
{
    protected $model = ReportInstitution::class;

    public function definition(): array
    {
        return [
            'report_id' => $this->faker->word(),
            'institution_id' => $this->faker->word(),
            'institution_branch_id' => $this->faker->word(),
            'status' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
