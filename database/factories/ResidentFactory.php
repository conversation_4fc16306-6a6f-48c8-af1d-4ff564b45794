<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\MunicipalityBranch;
use App\Models\Resident;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;

/** @extends Factory<Resident> */
class ResidentFactory extends Factory
{
    use FactoryHelpers;

    protected $model = Resident::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->generatePhoneNumber($this->faker),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => Hash::make('password'),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'municipality_branch_id' => MunicipalityBranch::factory(),
            'verified_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
