<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Institution;
use App\Models\InstitutionBranch;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InstitutionBranch> */
class InstitutionBranchFactory extends Factory
{
    use FactoryHelpers;

    protected $model = InstitutionBranch::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'location' => $this->generateLocation($this->faker),
            'phone' => $this->generatePhoneNumber($this->faker),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'institution_id' => Institution::factory(),
        ];
    }
}
