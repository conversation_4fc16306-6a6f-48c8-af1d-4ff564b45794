<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Institution;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Institution> */
class InstitutionFactory extends Factory
{
    use FactoryHelpers;

    protected $model = Institution::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'location' => $this->generateLocation($this->faker),
            'phone' => $this->generatePhoneNumber($this->faker),
            'email' => $this->faker->unique()->safeEmail(),
            'website' => $this->faker->word(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
