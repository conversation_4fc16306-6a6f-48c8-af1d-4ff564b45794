<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ReportCategory;
use App\Models\ReportType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReportType> */
class ReportTypeFactory extends Factory
{
    protected $model = ReportType::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'icon' => $this->faker->word(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'category_id' => ReportCategory::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
