<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Municipality;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Municipality> */
class MunicipalityFactory extends Factory
{
    use FactoryHelpers;

    protected $model = Municipality::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'location' => $this->generateLocation($this->faker),
            'phone' => $this->generatePhoneNumber($this->faker),
            'email' => $this->faker->unique()->safeEmail(),
            'website' => $this->faker->word(),
            'code' => $this->faker->word(),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
