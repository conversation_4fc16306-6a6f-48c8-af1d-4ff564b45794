<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Traits\FactoryHelpers;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MunicipalityBranch> */
class MunicipalityBranchFactory extends Factory
{
    use FactoryHelpers;

    protected $model = MunicipalityBranch::class;

    public function definition(): array
    {
        return [
            'municipality_id' => Municipality::Factory(),
            'name' => $this->faker->name(),
            'location' => $this->generateLocation($this->faker),
            'phone' => $this->generatePhoneNumber($this->faker),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'email' => $this->faker->unique()->safeEmail(),
        ];
    }
}
