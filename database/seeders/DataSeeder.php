<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Institution;
use App\Models\InstitutionBranch;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Models\Resident;
use App\Models\User;
use Illuminate\Database\Seeder;

class DataSeeder extends Seeder
{
    public function run(): void
    {
        // misrata
        $misrata = Municipality::factory()->create(['name' => 'بلدية مصراتة', 'status' => 'active', 'code' => 'M']);
        $zaroq = MunicipalityBranch::factory()->create(['name' => 'فرع الزروق', 'municipality_id' => $misrata->id, 'status' => 'active']);
        MunicipalityBranch::factory()->create(['name' => 'فرع الرملة', 'municipality_id' => $misrata->id, 'status' => 'inactive']);

        $gcc = Institution::factory()->create(['name' => 'شركة العامة للنظافة مصراتة', 'status' => 'active']);
        $gccBranch = InstitutionBranch::factory()->create(['name' => 'فرع الزروق', 'institution_id' => $gcc->id, 'status' => 'active']);

        $wgc = Institution::factory()->create(['name' => 'الشركة العامة للمياه', 'status' => 'active']);
        InstitutionBranch::factory()->create(['name' => 'فرع الزروق', 'institution_id' => $wgc->id, 'status' => 'active']);
        InstitutionBranch::factory()->create(['name' => 'فرع الرملة', 'institution_id' => $wgc->id, 'status' => 'active']);

        Resident::factory()->create(['email' => '<EMAIL>', 'municipality_branch_id' => $zaroq->id, 'status' => 'active']);
        Resident::factory()->create(['email' => '<EMAIL>', 'municipality_branch_id' => $zaroq->id, 'status' => 'active']);

        // add user to misrata
        $user = User::where('email', '<EMAIL>')->first();
        $misrata->users()->attach(['user_id' => $user->id]);
        $zaroq->users()->attach(['user_id' => $user->id]);
        $gcc->users()->attach(['user_id' => $user->id]);
        $gccBranch->users()->attach(['user_id' => $user->id]);
        $gccBranch->municipality_branches()->attach(['municipality_branch_id' => $zaroq->id]);

        // tripoli
        $tripoli = Municipality::factory()->create(['name' => 'بلدية طرابلس', 'status' => 'active', 'code' => 'T']);
        $city = MunicipalityBranch::factory()->create(['name' => 'فرع المدينة', 'municipality_id' => $tripoli->id, 'status' => 'active']);

        Resident::factory()->create(['email' => '<EMAIL>', 'municipality_branch_id' => $city->id, 'status' => 'active']);
    }
}
