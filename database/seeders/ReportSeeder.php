<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Institution;
use App\Models\InstitutionBranch;
use App\Models\Municipality;
use App\Models\MunicipalityBranch;
use App\Models\Report;
use App\Models\ReportCategory;
use App\Models\ReportType;
use App\Models\Resident;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReportSeeder extends Seeder
{
    public function run(): void
    {
        MunicipalityBranch::where('name', '=', 'فرع الزروق')->get();
        MunicipalityBranch::where('name', '=', 'فرع الرملة')->get();

        InstitutionBranch::where('name', '=', 'فرع الزروق')->get();
        InstitutionBranch::where('name', '=', 'فرع الرملة')->get();

        $institution_gcc = Institution::where('name', '=', 'شركة العامة للنظافة مصراتة')->get();
        $institution_wgc = Institution::where('name', '=', 'الشركة العامة للمياه')->get();

        ReportCategory::factory()->create(['name' => 'الشكاوى', 'status' => 'active']);
        $reportType = ReportType::factory()->create([
            'name' => ' انقطاع تيار',
            'category_id' => ReportCategory::first()->id,
            'status' => 'active',
        ]);

        $reportType->institution()->attach($institution_gcc->first()->id);
        $reportType->institution()->attach($institution_wgc->first()->id);

        $report_one = Report::factory()->create([
            'title' => 'الشكوة رقم 1',
            'content' => 'الشكاوى',
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
            'municipality_id' => Municipality::first()->id,
            'created_at' => now()->subDays(4),
        ]);

        $report_one->reportInstitutions()->create([
            'institution_id' => $institution_gcc->first()->id,
            'institution_branch_id' => InstitutionBranch::first()->id,
        ]);

        $report_one->reportInstitutions()->create([
            'institution_id' => $institution_wgc->first()->id,
        ]);

        $report_one->timelines()->create([
            'status' => 'assigned',
            'details' => json_encode('الشكاوى'),
            'notes' => 'تم تخصيص البلاغ لشركة النظافة',
            'report_id' => $report_one->id,
            'user_id' => User::first()->id,
            'user_type' => User::class,
        ]);

        $report_two = Report::factory()->create([
            'title' => 'الشكوة رقم 2',
            'content' => 'الشكاوى',
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
            'created_at' => now()->subDays(3),
        ]);

        $report_two->reportInstitutions()->create([
            'institution_id' => $institution_gcc->first()->id,
        ]);

        $report_two->reportInstitutions()->create([
            'institution_id' => $institution_wgc->first()->id,
            'status' => 'assigned',
        ]);

        $report_three = Report::factory()->create([
            'title' => 'الشكوة رقم 3',
            'content' => 'الشكاوى',
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
            'created_at' => now()->subDays(2),
        ]);

        $report_three->reportInstitutions()->create([
            'institution_id' => $institution_wgc->first()->id,
        ]);

        Report::factory()->create([
            'title' => 'الشكوة رقم 4',
            'content' => 'الشكاوى',
            'resident_id' => Resident::first()->id,
            'type_id' => ReportType::first()->id,
            'category_id' => ReportCategory::first()->id,
            'created_at' => now()->subDays(),
        ]);
    }
}
