<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('report_institutions', function (Blueprint $table): void {
            $table->dropForeign(['report_id']);

            $table->foreign('report_id')
                ->references('id')
                ->on('reports')
                ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('report_institutions', function (Blueprint $table): void {
            $table->dropForeign(['report_id']);

            $table->foreign('report_id')
                ->references('id')
                ->on('reports');
        });
    }
};
