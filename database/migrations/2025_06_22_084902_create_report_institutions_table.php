<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('report_institutions', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->foreignUuid('report_id')->references('id')->on('reports');
            $table->foreignUuid('institution_id')->references('id')->on('institutions');
            $table->foreignUuid('institution_branch_id')->nullable()->references('id')->on('institution_branches');
            $table->string('status')->default('created');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('report_institutions');
    }
};
