<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('residents_device_tokens', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->foreignUuid('resident_id')
                ->references('id')
                ->on('residents')
                ->onDelete('cascade');
            $table->string('token');
            $table->timestamp('last_seen');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('residents_device_tokens');
    }
};
