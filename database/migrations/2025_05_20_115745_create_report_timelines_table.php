<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('report_timelines', function (Blueprint $table): void {
            $table->uuid('id')->primary();
            $table->string('status')->default('created');
            $table->json('details')->nullable();
            $table->string('notes')->nullable();
            $table->foreignUuid('report_id')->references('id')->on('reports');
            $table->uuidMorphs('user');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('report_timelines');
    }
};
