<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resident_otp', function (Blueprint $table): void {
            $table->dropForeign(['resident_id']);

            $table->foreign('resident_id')
                ->references('id')
                ->on('residents')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resident_otp', function (Blueprint $table): void {
            $table->dropForeign(['resident_id']);

            $table->foreign('resident_id')
                ->references('id')
                ->on('residents');
        });
    }
};
