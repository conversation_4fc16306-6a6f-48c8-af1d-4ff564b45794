<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('report_type_institution', function (Blueprint $table): void {
            $table->foreignUuid('report_type_id');
            $table->foreignUuid('institution_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('report_type_institution');
    }
};
