# Name of your application. Used to uniquely configure containers.
service: municipality

# Name of the container image.
image: lamahco/municipality

# Deploy to these servers.
servers:
  web:
    hosts:
      - ************
    cmd: /usr/local/bin/entrypoint.sh
  # job:
  #   hosts:
  #     - ***********
  #   cmd: bin/jobs

# Enable SSL auto certification via Let's Encrypt and allow for multiple apps on a single web server.
# Remove this section when using multiple web servers and ensure you terminate SSL at your load balancer.
#
# Note: If using Cloudflare, set encryption mode in SSL/TLS setting to "Full" to enable CF-to-app encryption.
proxy:
  ssl: true
  host: mun.lamahtech.com
  # Proxy connects to your container on port 80 by default.
  app_port: 8080
  forward_headers: true
  # Proxy connects to your container on port 80 by default.
  # app_port: 3000

# Credentials for your image host.
registry:
  # Specify the registry server, if you're not using Docker Hub
  # server: registry.digitalocean.com / ghcr.io / ...
  username: la<PERSON><PERSON>

  # Always use an access token rather than real password (pulled from .kamal/secrets).
  password:
    - KAMAL_REGISTRY_PASSWORD

# Configure builder setup.
builder:
  arch: amd64
  dockerfile: docker/Dockerfile
  context: .
  # Pass in additional build args needed for your Dockerfile.
  # args:
  #   RUBY_VERSION: <%= ENV["RBENV_VERSION"] || ENV["rvm_ruby_string"] || "#{RUBY_ENGINE}-#{RUBY_ENGINE_VERSION}" %>

# Inject ENV variables into containers (secrets come from .kamal/secrets).
#
env:
  clear:
    RUN_MIGRATIONS: true

    APP_NAME: Municipality
    APP_ENV: productionAPP_DEBUG=false
    APP_URL: https://mun.lamahtech.com

    APP_LOCALE: ar
    APP_FALLBACK_LOCALE: en
    APP_FAKER_LOCALE: en_US

    APP_MAINTENANCE_DRIVER: file
    BCRYPT_ROUNDS: 12

    LOG_CHANNEL: stack
    LOG_STACK: single
    LOG_DEPRECATIONS_CHANNEL: "null"
    LOG_LEVEL: debug

    DB_CONNECTION: pgsql

    SESSION_DRIVER: database
    SESSION_LIFETIME: 120
    SESSION_ENCRYPT: false
    SESSION_PATH: /
    SESSION_DOMAIN: "null"

    BROADCAST_CONNECTION: log
    FILESYSTEM_DISK: local
    QUEUE_CONNECTION: database

    CACHE_STORE: database

    MEMCACHED_HOST: "127.0.0.1"

    AWS_DEFAULT_REGION: auto
    AWS_USE_PATH_STYLE_ENDPOINT: true

    VITE_APP_NAME: Municipality

    ESHAAR_OTP_LANGUAGE: ar
    ESHAAR_OTP_PAYMENT_METHOD: wallet
    ESHAAR_OTP_EXPIRY: 3
    ESHAAR_OTP_SENDER_NAME: Lamah
    ESHAAR_OTP_LENGTH: 4
    FIREBASE_CREDENTIALS: "lamah-municipality-app-firebase-adminsdk.json"
  secret:
    - FIREBASE_ADMINSDK_B64
    - APP_KEY
    - DB_HOST
    - DB_PORT
    - DB_DATABASE
    - DB_USERNAME
    - DB_PASSWORD
    - AWS_ACCESS_KEY_ID
    - AWS_SECRET_ACCESS_KEY
    - AWS_BUCKET
    - AWS_ENDPOINT
    - ESHAAR_API_KEY
    - ESHAAR_API_URL

# Aliases are triggered with "bin/kamal <alias>". You can overwrite arguments on invocation:
# "bin/kamal app logs -r job" will tail logs from the first server in the job section.
#
# aliases:
#   shell: app exec --interactive --reuse "bash"

# Use a different ssh user than root
#
# ssh:
#   user: app

# Use a persistent storage volume.
#
volumes:
  - "/var/www/municipality/storage:/app/storage"

# Bridge fingerprinted assets, like JS and CSS, between versions to avoid
# hitting 404 on in-flight requests. Combines all files from new and old
# version inside the asset_path.
#
asset_path: /app/public

# Configure rolling deploys by setting a wait time between batches of restarts.
#
# boot:
#   limit: 10 # Can also specify as a percentage of total hosts, such as "25%"
#   wait: 2

# Use accessory services (secrets come from .kamal/secrets).
#
accessories:
  db:
    service: postgres
    image: postgres:17
    host: ************
    port: 5432
    env:
#       clear:
#         MYSQL_ROOT_HOST: '%'
      secret:
        - POSTGRES_PASSWORD
        - POSTGRES_DB
        - POSTGRES_USER
#     files:
#       - config/mysql/production.cnf:/etc/mysql/my.cnf
#       - db/production.sql:/docker-entrypoint-initdb.d/setup.sql
    directories:
      - pg_data:/var/lib/postgresql/data
#   redis:
#     image: valkey/valkey:8
#     host: ***********
#     port: 6379
#     directories:
#       - data:/data
