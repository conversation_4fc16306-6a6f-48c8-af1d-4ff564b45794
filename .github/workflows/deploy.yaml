name: Deploy

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy
    if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
    runs-on: ubuntu-latest

    env:
      DOCKER_BUILDKIT: 1
      KAMAL_REGISTRY_PASSWORD: ${{ secrets.KAMAL_REGISTRY_PASSWORD }}
      APP_KEY: ${{ secrets.APP_KEY }}
      DB_HOST: ${{ secrets.DB_HOST }}
      DB_PORT: ${{ secrets.DB_PORT }}
      DB_DATABASE: ${{ secrets.DB_DATABASE }}
      DB_USERNAME: ${{ secrets.DB_USERNAME }}
      DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_BUCKET: ${{ secrets.A<PERSON>_BUCKET }}
      AWS_ENDPOINT: ${{ secrets.AWS_ENDPOINT }}
      ESHAAR_API_KEY: ${{ secrets.ESHAAR_API_KEY }}
      ESHAAR_API_URL: ${{ secrets.ESHAAR_API_URL }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.4.5
          bundler-cache: true

      - name: Install dependencies
        run: |
          gem install specific_install
          gem specific_install https://github.com/basecamp/kamal.git v2.7.0

      - uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Expose GitHub Runtime for cache
        uses: crazy-max/ghaction-github-runtime@v3

      - name: Prepare Firebase Admin SDK secret (base64)
        env:
          FIREBASE_ADMINSDK_JSON: ${{ secrets.FIREBASE_ADMINSDK_JSON }}
        run: |
          if [ -n "${FIREBASE_ADMINSDK_JSON}" ]; then
            echo "FIREBASE_ADMINSDK_B64=$(printf '%s' "$FIREBASE_ADMINSDK_JSON" | base64 -w 0)" >> $GITHUB_ENV
          else
            echo "FIREBASE_ADMINSDK_B64=" >> $GITHUB_ENV
          fi

      - name: Log in to Docker registry
        uses: docker/login-action@v3
        with:
          username: lamahco
          password: ${{ secrets.KAMAL_REGISTRY_PASSWORD }}

      - name: Set image tag
        run: |
          echo "IMAGE=lamahco/municipality" >> $GITHUB_ENV
          echo "IMAGE_TAG=${GITHUB_SHA}" >> $GITHUB_ENV

      # Build and push image with GHA cache. Kamal will use this tag directly,
      # avoiding a second build and ensuring cache effectiveness.
      - name: Build and push (with GHA cache)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: docker/Dockerfile
          platforms: linux/amd64
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: |
            ${{ env.IMAGE }}:latest
            ${{ env.IMAGE }}:${{ env.IMAGE_TAG }}
          labels: |
            service=municipality
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run deploy command
        run: kamal deploy --skip-push
